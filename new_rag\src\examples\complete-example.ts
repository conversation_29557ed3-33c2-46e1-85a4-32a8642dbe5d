/**
 * RAG-Anything 完整功能演示示例
 */

import { RAGAnything, DefaultRAGBackend } from '../core';
import { 
  createOpenAILLMFunction, 
  createOpenAIVisionFunction, 
  createOpenAIEmbeddingFunction,
  createOpenAIConfig
} from '../utils/openai';

async function main() {
  console.log('🚀 启动RAG-Anything完整功能演示');

  // 检查环境变量
  if (!process.env.OPENAI_API_KEY) {
    console.error('❌ 请设置OPENAI_API_KEY环境变量');
    console.log('示例: export OPENAI_API_KEY="your-api-key-here"');
    process.exit(1);
  }

  try {
    // 创建OpenAI配置
    const openaiConfig = createOpenAIConfig(process.env.OPENAI_API_KEY, {
      model: 'gpt-4o-mini',
      visionModel: 'gpt-4o',
      embeddingModel: 'text-embedding-3-large',
      maxTokens: 4000,
      temperature: 0.7
    });

    console.log('✅ OpenAI配置创建完成');

    // 创建模型函数
    const llmFunc = createOpenAILLMFunction(openaiConfig);
    const visionFunc = createOpenAIVisionFunction(openaiConfig);
    const embeddingFunc = createOpenAIEmbeddingFunction(openaiConfig);

    // 创建RAG后端
    const ragBackend = new DefaultRAGBackend(
      './rag_storage',
      embeddingFunc,
      llmFunc
    );

    // 创建RAG实例
    const rag = new RAGAnything({
      workingDir: './rag_storage',
      llmModelFunc: llmFunc,
      visionModelFunc: visionFunc,
      embeddingFunc: embeddingFunc,
      ragBackend: ragBackend
    });

    console.log('✅ RAG实例创建完成');

    // 初始化
    await rag.initialize();
    console.log('✅ RAG系统初始化完成');

    // 演示1: 文本内容插入
    console.log('\n📝 演示1: 文本内容插入...');
    const sampleTexts = [
      {
        content: `
人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。

AI的主要应用领域包括：
1. 机器学习 - 让计算机从数据中学习
2. 自然语言处理 - 理解和生成人类语言
3. 计算机视觉 - 分析和理解图像
4. 机器人技术 - 创建能够与物理世界交互的系统

深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。
        `,
        fileName: 'ai_introduction.txt',
        docId: 'ai_intro_001'
      },
      {
        content: `
机器学习（Machine Learning，ML）是人工智能的一个重要分支，专注于开发算法和统计模型，使计算机能够在没有明确编程的情况下学习和改进性能。

机器学习的主要类型：
1. 监督学习 - 使用标记数据训练模型
2. 无监督学习 - 从未标记数据中发现模式
3. 强化学习 - 通过与环境交互学习最优策略

常见的机器学习算法包括线性回归、决策树、随机森林、支持向量机和神经网络。
        `,
        fileName: 'machine_learning.txt',
        docId: 'ml_intro_001'
      }
    ];

    for (const text of sampleTexts) {
      await rag.insertTextContent(text.content, text.fileName, {
        docId: text.docId
      });
      console.log(`✅ 插入文本: ${text.fileName}`);
    }

    // 演示2: 查询功能
    console.log('\n🔍 演示2: 查询功能...');
    
    const queries = [
      '什么是人工智能？',
      'AI的主要应用领域有哪些？',
      '深度学习和机器学习的关系是什么？',
      '机器学习有哪些主要类型？',
      '常见的机器学习算法有哪些？'
    ];

    for (const query of queries) {
      console.log(`\n查询: ${query}`);
      const result = await rag.queryWithMultimodal(query, { mode: 'hybrid' });
      console.log(`答案: ${result.answer.substring(0, 300)}...`);
      console.log(`来源数量: ${result.sources?.length || 0}`);
    }

    // 演示3: 不同查询模式
    console.log('\n🔄 演示3: 不同查询模式...');
    const testQuery = '什么是机器学习？';
    
    const modes = ['local', 'global', 'hybrid'] as const;
    for (const mode of modes) {
      console.log(`\n模式: ${mode}`);
      const result = await rag.queryWithMultimodal(testQuery, { mode });
      console.log(`答案长度: ${result.answer.length} 字符`);
      console.log(`答案预览: ${result.answer.substring(0, 150)}...`);
    }

    // 演示4: 处理器信息
    console.log('\n📊 演示4: 处理器信息...');
    const processorInfo = rag.getProcessorInfo();
    console.log('处理器状态:', processorInfo.status);
    console.log('可用处理器:', Object.keys(processorInfo.processors || {}));
    console.log('模型配置:', processorInfo.models);

    // 演示5: 系统状态检查
    console.log('\n🔧 演示5: 系统状态检查...');
    console.log('RAG系统已初始化:', rag.isInitialized());
    console.log('MinerU安装状态:', await rag.checkMineruInstallation());

    console.log('\n🎉 所有演示完成！');

    // 演示统计信息
    console.log('\n📈 演示统计:');
    console.log(`- 插入文档数量: ${sampleTexts.length}`);
    console.log(`- 执行查询数量: ${queries.length + modes.length}`);
    console.log(`- 测试查询模式: ${modes.join(', ')}`);

  } catch (error) {
    console.error('❌ 演示过程中出错:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      if (error.stack) {
        console.error('错误堆栈:', error.stack);
      }
    }
  } finally {
    // 清理资源
    try {
      console.log('\n🧹 清理资源...');
      // 这里可以添加具体的清理逻辑
      console.log('✅ 资源清理完成');
    } catch (cleanupError) {
      console.error('❌ 清理过程中出错:', cleanupError);
    }
  }
}

/**
 * 演示批处理功能（可选）
 */
async function demonstrateBatchProcessing(rag: RAGAnything) {
  console.log('\n📁 演示批处理功能...');
  
  const documentsDir = './test_documents';
  
  try {
    const fs = await import('fs-extra');
    
    if (await fs.pathExists(documentsDir)) {
      const batchResult = await rag.processFolderComplete(documentsDir, {
        fileExtensions: ['.pdf', '.txt', '.md', '.docx'],
        recursive: true,
        maxWorkers: 2,
        displayStats: true
      });
      
      console.log(`✅ 批处理完成: ${batchResult.success}/${batchResult.total} 个文件处理成功`);
      
      if (batchResult.failed > 0) {
        console.log(`❌ 失败文件数量: ${batchResult.failed}`);
        batchResult.failedFiles.forEach(failure => {
          console.log(`  - ${failure.filePath}: ${failure.error}`);
        });
      }
    } else {
      console.log('ℹ️ 测试文档目录不存在，跳过批处理演示');
      console.log(`   创建目录 ${documentsDir} 并放入文档文件来测试批处理功能`);
    }
  } catch (error) {
    console.log('ℹ️ 批处理演示跳过:', error instanceof Error ? error.message : String(error));
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 主函数执行
if (require.main === module) {
  main().catch(error => {
    console.error('主函数执行失败:', error);
    process.exit(1);
  });
}

export { main as runCompleteExample };
