/**
 * 默认 RAG 后端实现
 * 提供基础的内存存储和简单的查询功能
 */

import fs from 'fs-extra';
import path from 'path';
import { 
  RAGBackend, 
  EntityInfo, 
  ChunkData, 
  QueryParams, 
  QueryResult,
  RelationshipInfo,
  VectorDatabase,
  KnowledgeGraph,
  NodeData,
  EmbeddingFunction,
  LLMModelFunction
} from '@/types';
import { getModuleLogger } from '@/utils/logger';
import { calculateStringHash } from '@/utils/fileUtils';

const logger = getModuleLogger('DefaultRAGBackend');

/**
 * 简单的内存向量数据库实现
 */
class MemoryVectorDatabase implements VectorDatabase {
  private data: Map<string, any> = new Map();
  private embeddings: Map<string, number[]> = new Map();
  private embeddingFunc: EmbeddingFunction | undefined;

  constructor(embeddingFunc?: EmbeddingFunction) {
    this.embeddingFunc = embeddingFunc;
  }

  async upsert(data: Record<string, any>): Promise<void> {
    for (const [id, item] of Object.entries(data)) {
      this.data.set(id, item);
      
      // 如果有嵌入函数，计算向量
      if (this.embeddingFunc && item.content) {
        try {
          const embeddings = await this.embeddingFunc([item.content]);
          if (embeddings.length > 0 && embeddings[0]) {
            this.embeddings.set(id, embeddings[0]);
          }
        } catch (error) {
          logger.warn(`Failed to compute embedding for ${id}:`, error);
        }
      }
    }
  }

  async search(query: string, topK: number = 5): Promise<any[]> {
    if (!this.embeddingFunc) {
      // 简单的文本匹配
      const results: any[] = [];
      const queryLower = query.toLowerCase();
      
      for (const [id, item] of this.data.entries()) {
        if (item.content && item.content.toLowerCase().includes(queryLower)) {
          results.push({ id, ...item, score: 1.0 });
        }
      }
      
      return results.slice(0, topK);
    }

    try {
      // 向量相似度搜索
      const queryEmbeddings = await this.embeddingFunc([query]);
      if (queryEmbeddings.length === 0 || !queryEmbeddings[0]) {
        return [];
      }

      const queryVector = queryEmbeddings[0];
      const results: Array<{ id: string; item: any; score: number }> = [];

      for (const [id, embedding] of this.embeddings.entries()) {
        if (embedding) {
          const similarity = this.cosineSimilarity(queryVector, embedding);
          const item = this.data.get(id);
          if (item) {
            results.push({ id, item, score: similarity });
          }
        }
      }

      // 按相似度排序并返回前 topK 个
      results.sort((a, b) => b.score - a.score);
      return results.slice(0, topK).map(r => ({ id: r.id, ...r.item, score: r.score }));

    } catch (error) {
      logger.error('Vector search failed:', error);
      return [];
    }
  }

  async delete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.data.delete(id);
      this.embeddings.delete(id);
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (!a || !b || a.length !== b.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      const aVal = a[i] || 0;
      const bVal = b[i] || 0;
      dotProduct += aVal * bVal;
      normA += aVal * aVal;
      normB += bVal * bVal;
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }
}

/**
 * 简单的内存知识图谱实现
 */
class MemoryKnowledgeGraph implements KnowledgeGraph {
  private nodes: Map<string, NodeData> = new Map();
  private edges: Map<string, Map<string, any>> = new Map();

  async addNode(nodeId: string, nodeData: NodeData): Promise<void> {
    this.nodes.set(nodeId, nodeData);
    if (!this.edges.has(nodeId)) {
      this.edges.set(nodeId, new Map());
    }
  }

  async addEdge(sourceId: string, targetId: string, edgeData: any): Promise<void> {
    if (!this.edges.has(sourceId)) {
      this.edges.set(sourceId, new Map());
    }
    if (!this.edges.has(targetId)) {
      this.edges.set(targetId, new Map());
    }
    
    this.edges.get(sourceId)!.set(targetId, edgeData);
  }

  async getNode(nodeId: string): Promise<NodeData | null> {
    return this.nodes.get(nodeId) || null;
  }

  async getNeighbors(nodeId: string): Promise<string[]> {
    const neighbors = this.edges.get(nodeId);
    return neighbors ? Array.from(neighbors.keys()) : [];
  }

  async removeNode(nodeId: string): Promise<void> {
    this.nodes.delete(nodeId);
    this.edges.delete(nodeId);
    
    // 删除指向该节点的边
    for (const [_, neighbors] of this.edges.entries()) {
      neighbors.delete(nodeId);
    }
  }

  async removeEdge(sourceId: string, targetId: string): Promise<void> {
    const neighbors = this.edges.get(sourceId);
    if (neighbors) {
      neighbors.delete(targetId);
    }
  }
}

/**
 * 默认 RAG 后端实现
 */
export class DefaultRAGBackend implements RAGBackend {
  private workingDir: string;
  private entities: Map<string, EntityInfo> = new Map();
  private chunks: Map<string, ChunkData> = new Map();
  private relationships: Map<string, RelationshipInfo> = new Map();
  private vectorDb: MemoryVectorDatabase;
  private knowledgeGraph: MemoryKnowledgeGraph;
  private llmModelFunc: LLMModelFunction | undefined;
  private embeddingFunc: EmbeddingFunction | undefined;
  private initialized: boolean = false;

  constructor(
    workingDir: string,
    embeddingFunc?: EmbeddingFunction,
    llmModelFunc?: LLMModelFunction
  ) {
    this.workingDir = workingDir;
    this.embeddingFunc = embeddingFunc;
    this.llmModelFunc = llmModelFunc;
    this.vectorDb = new MemoryVectorDatabase(embeddingFunc);
    this.knowledgeGraph = new MemoryKnowledgeGraph();
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 确保工作目录存在
      await fs.ensureDir(this.workingDir);
      
      // 尝试加载已存在的数据
      await this.loadPersistedData();
      
      this.initialized = true;
      logger.info('DefaultRAGBackend initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize DefaultRAGBackend:', error);
      throw error;
    }
  }

  async insertTextContent(
    content: string,
    filePath: string,
    options: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
      docId?: string;
    } = {}
  ): Promise<void> {
    try {
      const { splitByCharacter, splitByCharacterOnly = false, docId } = options;
      
      // 简单的文本分块
      const chunks = this.splitText(content, splitByCharacter, splitByCharacterOnly);
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        if (!chunk) continue;

        const chunkId = docId ? `${docId}_chunk_${i}` : calculateStringHash(chunk, 'chunk-');

        const chunkData: ChunkData = {
          tokens: this.estimateTokens(chunk),
          content: chunk,
          chunk_order_index: i,
          full_doc_id: docId || calculateStringHash(content, 'doc-'),
          file_path: filePath
        };
        
        await this.createTextChunk(chunkData);
        
        // 添加到向量数据库
        await this.vectorDb.upsert({
          [chunkId]: {
            content: chunk,
            file_path: filePath,
            chunk_order_index: i,
            type: 'text'
          }
        });
      }
      
      logger.info(`Inserted ${chunks.length} text chunks from ${filePath}`);
      
    } catch (error) {
      logger.error('Failed to insert text content:', error);
      throw error;
    }
  }

  async createEntity(entityInfo: EntityInfo, chunkId: string): Promise<void> {
    try {
      const entityId = entityInfo.entity_name;
      this.entities.set(entityId, {
        ...entityInfo,
        source_id: chunkId,
        created_at: Date.now()
      });

      // 添加到知识图谱
      const nodeData: NodeData = {
        entity_id: entityId,
        entity_type: entityInfo.entity_type,
        description: entityInfo.summary,
        source_id: chunkId,
        file_path: entityInfo.file_path || 'unknown',
        created_at: Date.now()
      };

      await this.knowledgeGraph.addNode(entityId, nodeData);

      // 添加到向量数据库
      await this.vectorDb.upsert({
        [calculateStringHash(entityId, 'ent-')]: {
          entity_name: entityId,
          entity_type: entityInfo.entity_type,
          content: `${entityId}\n${entityInfo.summary}`,
          source_id: chunkId,
          type: 'entity'
        }
      });

      // 自动提取实体关系（如果有LLM函数）
      await this.extractEntityRelationships(entityId, chunkId);

      logger.info(`Created entity: ${entityId}`);

    } catch (error) {
      logger.error('Failed to create entity:', error);
      throw error;
    }
  }

  async updateEntity(entityId: string, updates: Partial<EntityInfo>): Promise<void> {
    const existing = this.entities.get(entityId);
    if (existing) {
      this.entities.set(entityId, { ...existing, ...updates });
      logger.info(`Updated entity: ${entityId}`);
    }
  }

  async getEntity(entityId: string): Promise<EntityInfo | null> {
    return this.entities.get(entityId) || null;
  }

  async createRelationship(
    sourceEntityId: string,
    targetEntityId: string,
    relationshipType: string,
    description: string,
    metadata: any = {}
  ): Promise<void> {
    try {
      const relationshipId = calculateStringHash(
        `${sourceEntityId}_${targetEntityId}_${relationshipType}`,
        'rel-'
      );
      
      const relationship: RelationshipInfo = {
        id: relationshipId,
        sourceEntityId,
        targetEntityId,
        relationshipType,
        description,
        weight: metadata.weight || 1.0,
        metadata,
        createdAt: Date.now()
      };
      
      this.relationships.set(relationshipId, relationship);
      
      // 添加到知识图谱
      await this.knowledgeGraph.addEdge(sourceEntityId, targetEntityId, {
        type: relationshipType,
        description,
        weight: relationship.weight,
        id: relationshipId
      });
      
      // 添加到向量数据库
      await this.vectorDb.upsert({
        [relationshipId]: {
          src_id: sourceEntityId,
          tgt_id: targetEntityId,
          relationship_type: relationshipType,
          content: `${relationshipType}\t${sourceEntityId}\n${targetEntityId}\n${description}`,
          type: 'relationship'
        }
      });
      
      logger.info(`Created relationship: ${sourceEntityId} -> ${targetEntityId} (${relationshipType})`);
      
    } catch (error) {
      logger.error('Failed to create relationship:', error);
      throw error;
    }
  }

  async createTextChunk(chunkData: ChunkData): Promise<string> {
    const chunkId = calculateStringHash(chunkData.content, 'chunk-');
    this.chunks.set(chunkId, chunkData);
    return chunkId;
  }

  async getTextChunk(chunkId: string): Promise<ChunkData | null> {
    return this.chunks.get(chunkId) || null;
  }

  async query(query: string, params: QueryParams = {}): Promise<QueryResult> {
    try {
      const { mode = 'hybrid', topK = 5 } = params;
      
      // 搜索相关内容
      const searchResults = await this.vectorDb.search(query, topK);
      
      if (searchResults.length === 0) {
        return {
          answer: '抱歉，没有找到相关信息。',
          sources: [],
          metadata: { mode, searchResults: 0 }
        };
      }
      
      // 如果有 LLM 函数，生成答案
      if (this.llmModelFunc) {
        const context = searchResults
          .map(result => result.content)
          .join('\n\n');
        
        const prompt = `基于以下信息回答问题：

问题：${query}

相关信息：
${context}

请提供准确、有用的答案：`;
        
        const answer = await this.llmModelFunc(prompt);
        
        return {
          answer,
          sources: searchResults.map(r => r.file_path || 'unknown').filter(Boolean),
          metadata: {
            mode,
            searchResults: searchResults.length,
            topSources: searchResults.slice(0, 3)
          }
        };
      }
      
      // 简单的基于检索的答案
      const answer = `基于检索到的信息：\n\n${searchResults
        .slice(0, 3)
        .map((result, index) => `${index + 1}. ${result.content.substring(0, 200)}...`)
        .join('\n\n')}`;
      
      return {
        answer,
        sources: searchResults.map(r => r.file_path || 'unknown').filter(Boolean),
        metadata: { mode, searchResults: searchResults.length }
      };
      
    } catch (error) {
      logger.error('Query failed:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      // 保存数据到磁盘
      await this.persistData();
      
      // 清理内存
      this.entities.clear();
      this.chunks.clear();
      this.relationships.clear();
      
      logger.info('DefaultRAGBackend cleanup completed');
      
    } catch (error) {
      logger.error('Failed to cleanup DefaultRAGBackend:', error);
      throw error;
    }
  }

  private splitText(
    text: string, 
    splitByCharacter?: string, 
    splitByCharacterOnly: boolean = false
  ): string[] {
    if (splitByCharacter) {
      const chunks = text.split(splitByCharacter);
      if (splitByCharacterOnly) {
        return chunks.filter(chunk => chunk.trim().length > 0);
      }
    }
    
    // 默认按段落分割
    const paragraphs = text.split(/\n\s*\n/);
    const chunks: string[] = [];
    let currentChunk = '';
    const maxChunkSize = 1000; // 字符数
    
    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }
    
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks.filter(chunk => chunk.length > 0);
  }

  private estimateTokens(text: string): number {
    // 简单的 token 估算：平均每个 token 约 4 个字符
    return Math.ceil(text.length / 4);
  }

  private async loadPersistedData(): Promise<void> {
    try {
      const dataFile = path.join(this.workingDir, 'rag_data.json');
      
      if (await fs.pathExists(dataFile)) {
        const data = await fs.readJson(dataFile);
        
        // 恢复实体
        if (data.entities) {
          for (const [id, entity] of Object.entries(data.entities)) {
            this.entities.set(id, entity as EntityInfo);
          }
        }
        
        // 恢复文本块
        if (data.chunks) {
          for (const [id, chunk] of Object.entries(data.chunks)) {
            this.chunks.set(id, chunk as ChunkData);
          }
        }
        
        // 恢复关系
        if (data.relationships) {
          for (const [id, rel] of Object.entries(data.relationships)) {
            this.relationships.set(id, rel as RelationshipInfo);
          }
        }
        
        logger.info('Loaded persisted RAG data');
      }
    } catch (error) {
      logger.warn('Failed to load persisted data:', error);
    }
  }

  /**
   * 提取实体关系
   */
  private async extractEntityRelationships(newEntityId: string, chunkId: string): Promise<void> {
    if (!this.llmModelFunc) {
      return;
    }

    try {
      const chunk = this.chunks.get(chunkId);
      if (!chunk) {
        return;
      }

      // 获取同一文档中的其他实体
      const relatedEntities = Array.from(this.entities.values())
        .filter(entity =>
          entity.source_id !== chunkId &&
          entity.file_path === chunk.file_path &&
          entity.entity_name !== newEntityId
        );

      // 为每个相关实体分析关系
      for (const relatedEntity of relatedEntities.slice(0, 5)) { // 限制数量避免过多API调用
        try {
          const relationshipPrompt = `分析以下两个实体之间的关系：

实体1：${newEntityId}
类型1：${this.entities.get(newEntityId)?.entity_type}
描述1：${this.entities.get(newEntityId)?.summary}

实体2：${relatedEntity.entity_name}
类型2：${relatedEntity.entity_type}
描述2：${relatedEntity.summary}

上下文：${chunk.content.substring(0, 500)}

请判断这两个实体之间是否存在有意义的关系。如果存在，请以JSON格式返回：
{
  "has_relationship": true,
  "relationship_type": "关系类型（如：belongs_to, related_to, part_of, contains等）",
  "description": "关系描述",
  "confidence": 0.0-1.0
}

如果不存在有意义的关系，返回：
{"has_relationship": false}`;

          const response = await this.llmModelFunc(relationshipPrompt);
          const relationshipInfo = this.parseRelationshipResponse(response);

          if (relationshipInfo.has_relationship && relationshipInfo.confidence > 0.6) {
            // 创建双向关系
            await this.createRelationship(
              newEntityId,
              relatedEntity.entity_name,
              relationshipInfo.relationship_type,
              relationshipInfo.description,
              { confidence: relationshipInfo.confidence, auto_extracted: true }
            );

            // 如果是包含关系，也创建反向关系
            if (relationshipInfo.relationship_type === 'belongs_to') {
              await this.createRelationship(
                relatedEntity.entity_name,
                newEntityId,
                'contains',
                `Contains ${newEntityId}`,
                { confidence: relationshipInfo.confidence, auto_extracted: true }
              );
            }
          }

        } catch (error) {
          logger.warn(`Failed to extract relationship between ${newEntityId} and ${relatedEntity.entity_name}:`, error);
        }
      }

    } catch (error) {
      logger.error('Failed to extract entity relationships:', error);
    }
  }

  /**
   * 解析关系提取响应
   */
  private parseRelationshipResponse(response: string): any {
    try {
      // 尝试提取JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          has_relationship: parsed.has_relationship || false,
          relationship_type: parsed.relationship_type || 'related_to',
          description: parsed.description || '',
          confidence: parsed.confidence || 0.5
        };
      }
    } catch (error) {
      logger.warn('Failed to parse relationship response:', error);
    }

    // 默认返回无关系
    return { has_relationship: false };
  }

  private async persistData(): Promise<void> {
    try {
      const dataFile = path.join(this.workingDir, 'rag_data.json');

      const data = {
        entities: Object.fromEntries(this.entities),
        chunks: Object.fromEntries(this.chunks),
        relationships: Object.fromEntries(this.relationships),
        timestamp: Date.now()
      };

      await fs.writeJson(dataFile, data, { spaces: 2 });
      logger.info('Persisted RAG data to disk');

    } catch (error) {
      logger.warn('Failed to persist data:', error);
    }
  }
}
