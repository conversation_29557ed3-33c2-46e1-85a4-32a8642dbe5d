#!/usr/bin/env node

/**
 * 构建脚本
 */

const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

async function main() {
  console.log('🚀 开始构建 RAG-Anything TypeScript...');

  try {
    // 清理旧的构建文件
    console.log('📁 清理构建目录...');
    await fs.remove('./dist');

    // 编译TypeScript
    console.log('🔨 编译TypeScript...');
    execSync('npx tsc', { stdio: 'inherit' });

    // 解析路径别名
    console.log('🔗 解析路径别名...');
    execSync('npx tsc-alias', { stdio: 'inherit' });

    // 复制非TypeScript文件
    console.log('📋 复制资源文件...');
    await copyAssets();

    // 生成package.json用于发布
    console.log('📦 生成发布配置...');
    await generateDistPackageJson();

    console.log('✅ 构建完成！');
    console.log('📂 构建文件位于: ./dist');

  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

async function copyAssets() {
  const assetsToMove = [
    { src: './README.md', dest: './dist/README.md' },
    { src: './LICENSE', dest: './dist/LICENSE' }
  ];

  for (const asset of assetsToMove) {
    if (await fs.pathExists(asset.src)) {
      await fs.copy(asset.src, asset.dest);
      console.log(`  ✓ 复制 ${asset.src} -> ${asset.dest}`);
    }
  }
}

async function generateDistPackageJson() {
  const packageJson = await fs.readJson('./package.json');
  
  // 创建用于发布的package.json
  const distPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    main: 'index.js',
    types: 'index.d.ts',
    keywords: packageJson.keywords,
    author: packageJson.author,
    license: packageJson.license,
    repository: packageJson.repository,
    dependencies: packageJson.dependencies,
    engines: packageJson.engines,
    files: ['**/*']
  };

  await fs.writeJson('./dist/package.json', distPackageJson, { spaces: 2 });
  console.log('  ✓ 生成 ./dist/package.json');
}

if (require.main === module) {
  main();
}
