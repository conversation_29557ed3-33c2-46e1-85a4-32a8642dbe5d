/**
 * 多模态处理器使用示例
 */

import {
  ImageModalProcessor,
  TableModalProcessor,
  EquationModalProcessor,
  GenericModalProcessor
} from '../processors';
import {
  ImageContent,
  TableContent,
  EquationContent,
  LLMModelFunction,
  VisionModelFunction
} from '../types';
import { logger } from '../utils/logger';
import { mockLLMFunction, mockVisionFunction } from './basic-example';

/**
 * 图像处理器示例
 */
async function imageProcessorExample(): Promise<void> {
  try {
    logger.info('Starting image processor example');

    // 创建图像处理器
    const imageProcessor = new ImageModalProcessor({
      modalCaptionFunc: mockVisionFunction,
      maxRetries: 3,
      timeout: 30000
    });

    // 模拟图像内容
    const imageContent: ImageContent = {
      type: 'image',
      img_path: './test-image.jpg',
      img_caption: ['测试图像', '示例图片'],
      img_footnote: ['这是一个测试图像'],
      img_id: 'test_image_001',
      bbox: [100, 100, 500, 400]
    };

    // 处理图像内容
    try {
      const [description, entityInfo] = await imageProcessor.processMultimodalContent(
        imageContent,
        'image',
        'test-document.pdf',
        'TestImage'
      );

      logger.info('Image processing result:');
      logger.info('Description:', description);
      logger.info('Entity Info:', entityInfo);

    } catch (error) {
      logger.warn('Image processing failed (expected if test image not found):', error);
    }

  } catch (error) {
    logger.error('Image processor example failed:', error);
    throw error;
  }
}

/**
 * 表格处理器示例
 */
async function tableProcessorExample(): Promise<void> {
  try {
    logger.info('Starting table processor example');

    // 创建表格处理器
    const tableProcessor = new TableModalProcessor({
      modalCaptionFunc: mockLLMFunction,
      maxRetries: 3,
      timeout: 30000
    });

    // 模拟表格内容
    const tableContent: TableContent = {
      type: 'table',
      table_body: `
| 产品名称 | 销售额 | 增长率 |
|----------|--------|--------|
| 产品A    | 1000万 | 15%    |
| 产品B    | 800万  | 8%     |
| 产品C    | 1200万 | 22%    |
| 总计     | 3000万 | 15%    |
      `,
      table_caption: ['2024年产品销售数据'],
      table_footnote: ['数据截至2024年12月'],
      table_id: 'sales_table_001',
      bbox: [50, 200, 600, 350]
    };

    // 处理表格内容
    const [description, entityInfo] = await tableProcessor.processMultimodalContent(
      tableContent,
      'table',
      'sales-report.pdf',
      'SalesTable2024'
    );

    logger.info('Table processing result:');
    logger.info('Description:', description);
    logger.info('Entity Info:', entityInfo);

  } catch (error) {
    logger.error('Table processor example failed:', error);
    throw error;
  }
}

/**
 * 公式处理器示例
 */
async function equationProcessorExample(): Promise<void> {
  try {
    logger.info('Starting equation processor example');

    // 创建公式处理器
    const equationProcessor = new EquationModalProcessor({
      modalCaptionFunc: mockLLMFunction,
      maxRetries: 3,
      timeout: 30000
    });

    // 模拟公式内容
    const equationContent: EquationContent = {
      type: 'equation',
      latex: '\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}',
      equation_caption: ['高斯积分公式'],
      equation_footnote: ['这是概率论中的重要公式'],
      equation_id: 'gaussian_integral_001',
      bbox: [100, 150, 400, 200]
    };

    // 处理公式内容
    const [description, entityInfo] = await equationProcessor.processMultimodalContent(
      equationContent,
      'equation',
      'math-textbook.pdf',
      'GaussianIntegral'
    );

    logger.info('Equation processing result:');
    logger.info('Description:', description);
    logger.info('Entity Info:', entityInfo);

  } catch (error) {
    logger.error('Equation processor example failed:', error);
    throw error;
  }
}

/**
 * 通用处理器示例
 */
async function genericProcessorExample(): Promise<void> {
  try {
    logger.info('Starting generic processor example');

    // 创建通用处理器
    const genericProcessor = new GenericModalProcessor({
      modalCaptionFunc: mockLLMFunction,
      maxRetries: 3,
      timeout: 30000
    });

    // 模拟自定义内容
    const customContent = {
      type: 'generic' as const,
      text: '自定义数据结构示例',
      title: '自定义数据结构',
      description: '这是一个自定义的数据结构示例',
      data: {
        values: [1, 2, 3, 4, 5],
        metadata: {
          source: 'test',
          timestamp: Date.now()
        }
      },
      custom_field: '自定义字段值'
    };

    // 处理自定义内容
    const [description, entityInfo] = await genericProcessor.processMultimodalContent(
      customContent as any,
      'generic',
      'custom-data.json',
      'CustomDataStructure'
    );

    logger.info('Generic processing result:');
    logger.info('Description:', description);
    logger.info('Entity Info:', entityInfo);

    // 测试自定义内容处理
    const customResult = await genericProcessor.processCustomContent(
      {
        type: 'audio',
        file_path: './audio.mp3',
        duration: 120,
        transcript: '这是音频文件的转录文本'
      },
      'audio',
      'audio-file.mp3',
      'AudioContent'
    );

    logger.info('Custom content processing result:');
    logger.info('Description:', customResult[0]);
    logger.info('Entity Info:', customResult[1]);

  } catch (error) {
    logger.error('Generic processor example failed:', error);
    throw error;
  }
}

/**
 * 处理器性能测试
 */
async function processorPerformanceTest(): Promise<void> {
  try {
    logger.info('Starting processor performance test');

    const tableProcessor = new TableModalProcessor({
      modalCaptionFunc: mockLLMFunction,
      maxRetries: 3,
      timeout: 30000
    });

    // 创建多个测试表格
    const testTables: TableContent[] = [];
    for (let i = 0; i < 5; i++) {
      testTables.push({
        type: 'table',
        table_body: `
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 值${i}1 | 值${i}2 | 值${i}3 |
| 值${i}4 | 值${i}5 | 值${i}6 |
        `,
        table_caption: [`测试表格 ${i + 1}`],
        table_id: `test_table_${i + 1}`
      });
    }

    // 测试处理时间
    const startTime = Date.now();
    const results = [];

    for (let i = 0; i < testTables.length; i++) {
      const itemStartTime = Date.now();
      
      try {
        const table = testTables[i];
        if (!table) {
          throw new Error(`Table ${i} is undefined`);
        }

        const result = await tableProcessor.processMultimodalContent(
          table,
          'table',
          'performance-test.pdf',
          `TestTable${i + 1}`
        );
        
        const itemEndTime = Date.now();
        results.push({
          index: i,
          success: true,
          processingTime: itemEndTime - itemStartTime,
          result
        });

      } catch (error) {
        const itemEndTime = Date.now();
        results.push({
          index: i,
          success: false,
          processingTime: itemEndTime - itemStartTime,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    const totalTime = Date.now() - startTime;

    logger.info('Performance test results:');
    logger.info(`Total time: ${totalTime}ms`);
    logger.info(`Average time per item: ${totalTime / testTables.length}ms`);
    logger.info(`Success rate: ${results.filter(r => r.success).length}/${results.length}`);

    results.forEach((result, index) => {
      logger.info(`Item ${index + 1}: ${result.success ? 'SUCCESS' : 'FAILED'} (${result.processingTime}ms)`);
    });

  } catch (error) {
    logger.error('Processor performance test failed:', error);
    throw error;
  }
}

// 主函数
async function main(): Promise<void> {
  try {
    logger.info('=== Processor Examples ===');

    await imageProcessorExample();
    
    logger.info('\n=== Table Processor Example ===');
    await tableProcessorExample();
    
    logger.info('\n=== Equation Processor Example ===');
    await equationProcessorExample();
    
    logger.info('\n=== Generic Processor Example ===');
    await genericProcessorExample();
    
    logger.info('\n=== Processor Performance Test ===');
    await processorPerformanceTest();

    logger.info('All processor examples completed successfully!');

  } catch (error) {
    logger.error('Processor examples failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export {
  imageProcessorExample,
  tableProcessorExample,
  equationProcessorExample,
  genericProcessorExample,
  processorPerformanceTest
};
