/**
 * OpenAI集成工具函数
 */

import { LLMModelFunction, VisionModelFunction, EmbeddingFunction } from '@/types';

/**
 * OpenAI配置接口
 */
export interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  model?: string;
  visionModel?: string;
  embeddingModel?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
}

/**
 * OpenAI API响应接口
 */
interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

/**
 * OpenAI嵌入响应接口
 */
interface OpenAIEmbeddingResponse {
  data: Array<{
    embedding: number[];
  }>;
}

/**
 * 创建OpenAI LLM函数
 */
export function createOpenAILLMFunction(config: OpenAIConfig): LLMModelFunction {
  const {
    apiKey,
    baseURL = 'https://api.openai.com/v1',
    model = 'gpt-4o-mini',
    maxTokens = 4000,
    temperature = 0.7,
    timeout = 30000
  } = config;

  return async (prompt: string, systemPrompt?: string, historyMessages?: any[]): Promise<string> => {
    try {
      const messages: any[] = [];
      
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }
      
      if (historyMessages && historyMessages.length > 0) {
        messages.push(...historyMessages);
      }
      
      messages.push({ role: 'user', content: prompt });

      const response = await fetch(`${baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model,
          messages,
          max_tokens: maxTokens,
          temperature,
          stream: false
        }),
        signal: AbortSignal.timeout(timeout)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: OpenAIResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from OpenAI API');
      }

      return data.choices[0].message.content;

    } catch (error) {
      console.error('OpenAI LLM function error:', error);
      throw new Error(`Failed to call OpenAI LLM: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * 创建OpenAI视觉函数
 */
export function createOpenAIVisionFunction(config: OpenAIConfig): VisionModelFunction {
  const {
    apiKey,
    baseURL = 'https://api.openai.com/v1',
    visionModel = 'gpt-4o',
    maxTokens = 4000,
    temperature = 0.7,
    timeout = 30000
  } = config;

  return async (
    prompt: string, 
    systemPrompt?: string, 
    historyMessages?: any[], 
    imageData?: string
  ): Promise<string> => {
    try {
      const messages: any[] = [];
      
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }
      
      if (historyMessages && historyMessages.length > 0) {
        messages.push(...historyMessages);
      }
      
      // 构建包含图像的消息
      const userMessage: any = {
        role: 'user',
        content: []
      };

      // 添加文本内容
      userMessage.content.push({
        type: 'text',
        text: prompt
      });

      // 如果有图像数据，添加图像
      if (imageData) {
        userMessage.content.push({
          type: 'image_url',
          image_url: {
            url: `data:image/jpeg;base64,${imageData}`,
            detail: 'high'
          }
        });
      }

      messages.push(userMessage);

      const response = await fetch(`${baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: visionModel,
          messages,
          max_tokens: maxTokens,
          temperature,
          stream: false
        }),
        signal: AbortSignal.timeout(timeout)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenAI Vision API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: OpenAIResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from OpenAI Vision API');
      }

      return data.choices[0].message.content;

    } catch (error) {
      console.error('OpenAI Vision function error:', error);
      throw new Error(`Failed to call OpenAI Vision: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * 创建OpenAI嵌入函数
 */
export function createOpenAIEmbeddingFunction(config: OpenAIConfig): EmbeddingFunction {
  const {
    apiKey,
    baseURL = 'https://api.openai.com/v1',
    embeddingModel = 'text-embedding-3-large',
    timeout = 30000
  } = config;

  return async (texts: string[]): Promise<number[][]> => {
    try {
      if (texts.length === 0) {
        return [];
      }

      // OpenAI嵌入API一次最多处理2048个文本
      const batchSize = 100;
      const results: number[][] = [];

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        
        const response = await fetch(`${baseURL}/embeddings`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: embeddingModel,
            input: batch,
            encoding_format: 'float'
          }),
          signal: AbortSignal.timeout(timeout)
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`OpenAI Embedding API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data: OpenAIEmbeddingResponse = await response.json();
        
        if (!data.data || data.data.length === 0) {
          throw new Error('No embeddings returned from OpenAI API');
        }

        // 添加这批结果
        for (const item of data.data) {
          results.push(item.embedding);
        }
      }

      return results;

    } catch (error) {
      console.error('OpenAI Embedding function error:', error);
      throw new Error(`Failed to call OpenAI Embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * 验证OpenAI配置
 */
export function validateOpenAIConfig(config: OpenAIConfig): void {
  if (!config.apiKey) {
    throw new Error('OpenAI API key is required');
  }

  if (config.apiKey.length < 10) {
    throw new Error('Invalid OpenAI API key format');
  }

  if (config.baseURL && !config.baseURL.startsWith('http')) {
    throw new Error('Invalid OpenAI base URL format');
  }

  if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 128000)) {
    throw new Error('Invalid maxTokens value (must be between 1 and 128000)');
  }

  if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
    throw new Error('Invalid temperature value (must be between 0 and 2)');
  }
}

/**
 * 创建完整的OpenAI配置
 */
export function createOpenAIConfig(apiKey: string, options: Partial<OpenAIConfig> = {}): OpenAIConfig {
  const config: OpenAIConfig = {
    apiKey,
    baseURL: options.baseURL || 'https://api.openai.com/v1',
    model: options.model || 'gpt-4o-mini',
    visionModel: options.visionModel || 'gpt-4o',
    embeddingModel: options.embeddingModel || 'text-embedding-3-large',
    maxTokens: options.maxTokens || 4000,
    temperature: options.temperature || 0.7,
    timeout: options.timeout || 30000
  };

  validateOpenAIConfig(config);
  return config;
}
