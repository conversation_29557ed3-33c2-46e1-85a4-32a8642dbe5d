/**
 * 使用OpenAI API的RAG-Anything示例
 */

import { RAGAnything } from '../core';
import { LLMModelFunction, VisionModelFunction, EmbeddingFunction } from '../types';
import { logger } from '../utils/logger';

// 注意：需要安装 npm install axios
// 并设置环境变量 OPENAI_API_KEY

interface OpenAIConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
  embeddingModel?: string;
  visionModel?: string;
}

/**
 * 创建OpenAI LLM函数
 */
function createOpenAILLMFunction(config: OpenAIConfig): LLMModelFunction {
  return async (
    prompt: string,
    systemPrompt?: string,
    historyMessages?: any[]
  ): Promise<string> => {
    const axios = require('axios');
    
    const messages = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    if (historyMessages && historyMessages.length > 0) {
      messages.push(...historyMessages);
    }
    
    messages.push({ role: 'user', content: prompt });

    try {
      const response = await axios.post(
        `${config.baseUrl || 'https://api.openai.com'}/v1/chat/completions`,
        {
          model: config.model || 'gpt-4o-mini',
          messages,
          temperature: 0.7,
          max_tokens: 2000
        },
        {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.choices[0].message.content;

    } catch (error) {
      logger.error('OpenAI LLM API call failed:', error);
      throw new Error(`OpenAI LLM API call failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * 创建OpenAI Vision函数
 */
function createOpenAIVisionFunction(config: OpenAIConfig): VisionModelFunction {
  return async (
    prompt: string,
    systemPrompt?: string,
    historyMessages?: any[],
    imageData?: string
  ): Promise<string> => {
    const axios = require('axios');
    
    const messages = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    if (historyMessages && historyMessages.length > 0) {
      messages.push(...historyMessages);
    }

    // 构建用户消息
    const userMessage: any = {
      role: 'user',
      content: []
    };

    // 添加文本内容
    if (prompt) {
      userMessage.content.push({
        type: 'text',
        text: prompt
      });
    }

    // 添加图像内容
    if (imageData) {
      userMessage.content.push({
        type: 'image_url',
        image_url: {
          url: `data:image/jpeg;base64,${imageData}`
        }
      });
    }

    messages.push(userMessage);

    try {
      const response = await axios.post(
        `${config.baseUrl || 'https://api.openai.com'}/v1/chat/completions`,
        {
          model: config.visionModel || 'gpt-4o',
          messages,
          temperature: 0.7,
          max_tokens: 2000
        },
        {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.choices[0].message.content;

    } catch (error) {
      logger.error('OpenAI Vision API call failed:', error);
      throw new Error(`OpenAI Vision API call failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * 创建OpenAI嵌入函数
 */
function createOpenAIEmbeddingFunction(config: OpenAIConfig): EmbeddingFunction {
  return async (texts: string[]): Promise<number[][]> => {
    const axios = require('axios');

    try {
      const response = await axios.post(
        `${config.baseUrl || 'https://api.openai.com'}/v1/embeddings`,
        {
          model: config.embeddingModel || 'text-embedding-3-large',
          input: texts
        },
        {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data.data.map((item: any) => item.embedding);

    } catch (error) {
      logger.error('OpenAI Embedding API call failed:', error);
      throw new Error(`OpenAI Embedding API call failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * OpenAI集成示例
 */
async function openaiIntegrationExample(): Promise<void> {
  try {
    logger.info('Starting OpenAI integration example');

    // 从环境变量获取API密钥
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    const openaiConfig: OpenAIConfig = {
      apiKey,
      model: 'gpt-4o-mini',
      visionModel: 'gpt-4o',
      embeddingModel: 'text-embedding-3-large',
      ...(process.env.OPENAI_BASE_URL && { baseURL: process.env.OPENAI_BASE_URL })
    };

    // 创建RAGAnything实例
    const rag = new RAGAnything({
      workingDir: './rag_storage_openai',
      llmModelFunc: createOpenAILLMFunction(openaiConfig),
      visionModelFunc: createOpenAIVisionFunction(openaiConfig),
      embeddingFunc: createOpenAIEmbeddingFunction(openaiConfig)
    });

    // 初始化
    await rag.initialize();
    logger.info('RAGAnything with OpenAI initialized successfully');

    // 处理文档
    const testDocument = './test-document.pdf';
    try {
      await rag.processDocumentComplete(testDocument, {
        outputDir: './openai_output',
        parseMethod: 'auto',
        displayStats: true
      });
      logger.info(`Document processed with OpenAI: ${testDocument}`);
    } catch (error) {
      logger.warn(`Test document processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    // 执行查询
    const queries = [
      '文档的主要内容是什么？',
      '文档中有哪些图表和数据？',
      '请总结文档中的关键发现。'
    ];

    for (const query of queries) {
      try {
        const result = await rag.queryWithMultimodal(query, { mode: 'hybrid' });
        logger.info(`Query: ${query}`);
        logger.info(`Answer: ${result.answer}`);
        logger.info('---');
      } catch (error) {
        logger.error(`Query failed: ${query}`, error);
      }
    }

    // 清理
    await rag.cleanup();
    logger.info('OpenAI integration example completed');

  } catch (error) {
    logger.error('OpenAI integration example failed:', error);
    throw error;
  }
}

/**
 * 批量处理与OpenAI示例
 */
async function openaiBatchProcessingExample(): Promise<void> {
  try {
    logger.info('Starting OpenAI batch processing example');

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    const openaiConfig: OpenAIConfig = {
      apiKey,
      model: 'gpt-4o-mini',
      visionModel: 'gpt-4o',
      embeddingModel: 'text-embedding-3-large'
    };

    const rag = new RAGAnything({
      workingDir: './rag_storage_openai_batch',
      llmModelFunc: createOpenAILLMFunction(openaiConfig),
      visionModelFunc: createOpenAIVisionFunction(openaiConfig),
      embeddingFunc: createOpenAIEmbeddingFunction(openaiConfig)
    });

    await rag.initialize();

    // 批量处理
    const batchResult = await rag.processFolderComplete('./documents', {
      outputDir: './openai_batch_output',
      fileExtensions: ['.pdf', '.docx', '.jpg', '.png'],
      recursive: true,
      maxWorkers: 1, // 限制并发以避免API限制
      parseMethod: 'auto'
    });

    logger.info('OpenAI batch processing result:', batchResult);

    await rag.cleanup();

  } catch (error) {
    logger.error('OpenAI batch processing example failed:', error);
    throw error;
  }
}

// 主函数
async function main(): Promise<void> {
  try {
    logger.info('=== OpenAI Integration Examples ===');

    await openaiIntegrationExample();
    
    logger.info('\n=== OpenAI Batch Processing Example ===');
    await openaiBatchProcessingExample();

    logger.info('All OpenAI examples completed successfully!');

  } catch (error) {
    logger.error('OpenAI examples failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export {
  openaiIntegrationExample,
  openaiBatchProcessingExample,
  createOpenAILLMFunction,
  createOpenAIVisionFunction,
  createOpenAIEmbeddingFunction
};
