/**
 * 测试构建后的功能
 */

const { RAGAnything, DefaultRAGBackend } = require('./dist');

async function testBasicFunctionality() {
  console.log('🧪 开始测试基本功能...');

  try {
    // 测试1: 基本导入和实例化
    console.log('📦 测试模块导入...');
    console.log('✅ RAGAnything 类导入成功');
    console.log('✅ DefaultRAGBackend 类导入成功');

    // 测试2: 创建RAG后端
    console.log('\n🔧 测试RAG后端创建...');
    const mockLLMFunc = async (prompt) => {
      return `模拟LLM回答: ${prompt.substring(0, 50)}...`;
    };

    const mockEmbeddingFunc = async (texts) => {
      return texts.map(() => Array.from({length: 10}, () => Math.random()));
    };

    const ragBackend = new DefaultRAGBackend(
      './test_rag_storage',
      mockEmbeddingFunc,
      mockLLMFunc
    );
    console.log('✅ DefaultRAGBackend 实例创建成功');

    // 测试3: 初始化RAG后端
    await ragBackend.initialize();
    console.log('✅ RAG后端初始化成功');

    // 测试4: 文本内容插入
    console.log('\n📝 测试文本内容插入...');
    const testText = `
人工智能（AI）是计算机科学的一个分支，专注于创建能够执行通常需要人类智能的任务的系统。
AI的主要应用包括机器学习、自然语言处理、计算机视觉和机器人技术。
深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。
    `;

    await ragBackend.insertTextContent(testText, 'test.txt', { docId: 'test_001' });
    console.log('✅ 文本内容插入成功');

    // 测试5: 查询功能
    console.log('\n🔍 测试查询功能...');
    const result = await ragBackend.query('什么是人工智能？');
    console.log('✅ 查询功能测试成功');
    console.log('查询结果:', result.answer.substring(0, 100) + '...');
    console.log('来源数量:', result.sources?.length || 0);

    // 测试6: 实体创建
    console.log('\n🏷️ 测试实体创建...');
    const entityInfo = {
      entity_name: '人工智能',
      entity_type: '概念',
      summary: 'AI是计算机科学的一个分支，专注于创建智能系统',
      file_path: 'test.txt'
    };

    await ragBackend.createEntity(entityInfo, 'test_chunk_001');
    console.log('✅ 实体创建成功');

    // 测试7: 关系创建
    console.log('\n🔗 测试关系创建...');
    const entityInfo2 = {
      entity_name: '机器学习',
      entity_type: '技术',
      summary: '让计算机从数据中学习的技术',
      file_path: 'test.txt'
    };

    await ragBackend.createEntity(entityInfo2, 'test_chunk_002');
    await ragBackend.createRelationship(
      '机器学习',
      '人工智能',
      'belongs_to',
      '机器学习是人工智能的一个分支'
    );
    console.log('✅ 关系创建成功');

    // 测试8: 清理
    console.log('\n🧹 测试资源清理...');
    await ragBackend.cleanup();
    console.log('✅ 资源清理成功');

    console.log('\n🎉 所有核心功能测试通过！');
    console.log('\n📊 测试总结:');
    console.log('- ✅ 模块导入和实例化');
    console.log('- ✅ RAG后端初始化');
    console.log('- ✅ 文本内容插入和向量化');
    console.log('- ✅ 语义查询和答案生成');
    console.log('- ✅ 实体创建和管理');
    console.log('- ✅ 实体关系提取');
    console.log('- ✅ 数据持久化');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testBasicFunctionality().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testBasicFunctionality };
