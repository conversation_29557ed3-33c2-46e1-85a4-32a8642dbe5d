/**
 * LightRAG兼容的RAG后端实现
 * 模拟LightRAG的核心功能和接口
 */

import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { 
  RAGBackend, 
  EntityInfo, 
  ChunkData, 
  QueryParams, 
  QueryResult,
  RelationshipInfo,
  NodeData,
  EmbeddingFunction,
  LLMModelFunction
} from '@/types';
import { getModuleLogger } from '@/utils/logger';
import { calculateStringHash } from '@/utils/fileUtils';

const logger = getModuleLogger('LightRAGBackend');

/**
 * 文本块存储接口
 */
interface TextChunkStorage {
  upsert(data: Record<string, ChunkData>): Promise<void>;
  get(id: string): Promise<ChunkData | null>;
  delete(ids: string[]): Promise<void>;
}

/**
 * 向量数据库接口
 */
interface VectorDatabase {
  upsert(data: Record<string, any>): Promise<void>;
  search(query: string, topK?: number): Promise<any[]>;
  delete(ids: string[]): Promise<void>;
}

/**
 * 知识图谱接口
 */
interface KnowledgeGraph {
  upsertNode(nodeId: string, nodeData: NodeData): Promise<void>;
  upsertEdge(sourceId: string, targetId: string, edgeData: any): Promise<void>;
  getNode(nodeId: string): Promise<NodeData | null>;
  getNeighbors(nodeId: string): Promise<string[]>;
  removeNode(nodeId: string): Promise<void>;
  removeEdge(sourceId: string, targetId: string): Promise<void>;
}

/**
 * LightRAG兼容的文本块存储实现
 */
class LightRAGTextChunkStorage implements TextChunkStorage {
  private chunks: Map<string, ChunkData> = new Map();
  private persistPath: string;

  constructor(workingDir: string) {
    this.persistPath = path.join(workingDir, 'text_chunks.json');
  }

  async upsert(data: Record<string, ChunkData>): Promise<void> {
    for (const [id, chunk] of Object.entries(data)) {
      this.chunks.set(id, chunk);
    }
    await this.persist();
  }

  async get(id: string): Promise<ChunkData | null> {
    return this.chunks.get(id) || null;
  }

  async delete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.chunks.delete(id);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.chunks = new Map(Object.entries(data));
      }
    } catch (error) {
      logger.warn('Failed to load text chunks:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = Object.fromEntries(this.chunks);
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist text chunks:', error);
    }
  }
}

/**
 * LightRAG兼容的向量数据库实现
 */
class LightRAGVectorDatabase implements VectorDatabase {
  private data: Map<string, any> = new Map();
  private embeddings: Map<string, number[]> = new Map();
  private embeddingFunc: EmbeddingFunction | undefined;
  private persistPath: string;

  constructor(workingDir: string, embeddingFunc?: EmbeddingFunction) {
    this.embeddingFunc = embeddingFunc;
    this.persistPath = path.join(workingDir, 'vector_db.json');
  }

  async upsert(data: Record<string, any>): Promise<void> {
    for (const [id, item] of Object.entries(data)) {
      this.data.set(id, item);
      
      // 计算向量
      if (this.embeddingFunc && item.content) {
        try {
          const embeddings = await this.embeddingFunc([item.content]);
          if (embeddings.length > 0 && embeddings[0]) {
            this.embeddings.set(id, embeddings[0]);
          }
        } catch (error) {
          logger.warn(`Failed to compute embedding for ${id}:`, error);
        }
      }
    }
    await this.persist();
  }

  async search(query: string, topK: number = 5): Promise<any[]> {
    if (!this.embeddingFunc) {
      // 简单文本匹配
      const results: any[] = [];
      const queryLower = query.toLowerCase();
      
      for (const [id, item] of this.data.entries()) {
        if (item.content && item.content.toLowerCase().includes(queryLower)) {
          results.push({ id, ...item, score: 1.0 });
        }
      }
      
      return results.slice(0, topK);
    }

    try {
      const queryEmbeddings = await this.embeddingFunc([query]);
      if (queryEmbeddings.length === 0 || !queryEmbeddings[0]) {
        return [];
      }

      const queryVector = queryEmbeddings[0];
      const results: Array<{ id: string; item: any; score: number }> = [];

      for (const [id, embedding] of this.embeddings.entries()) {
        if (embedding) {
          const similarity = this.cosineSimilarity(queryVector, embedding);
          const item = this.data.get(id);
          if (item) {
            results.push({ id, item, score: similarity });
          }
        }
      }

      results.sort((a, b) => b.score - a.score);
      return results.slice(0, topK).map(r => ({ id: r.id, ...r.item, score: r.score }));

    } catch (error) {
      logger.error('Vector search failed:', error);
      return [];
    }
  }

  async delete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.data.delete(id);
      this.embeddings.delete(id);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.data = new Map(Object.entries(data.data || {}));
        this.embeddings = new Map(Object.entries(data.embeddings || {}));
      }
    } catch (error) {
      logger.warn('Failed to load vector database:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = {
        data: Object.fromEntries(this.data),
        embeddings: Object.fromEntries(this.embeddings),
        timestamp: Date.now()
      };
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist vector database:', error);
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (!a || !b || a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      const aVal = a[i] || 0;
      const bVal = b[i] || 0;
      dotProduct += aVal * bVal;
      normA += aVal * aVal;
      normB += bVal * bVal;
    }
    
    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }
}

/**
 * LightRAG兼容的知识图谱实现
 */
class LightRAGKnowledgeGraph implements KnowledgeGraph {
  private nodes: Map<string, NodeData> = new Map();
  private edges: Map<string, Map<string, any>> = new Map();
  private persistPath: string;

  constructor(workingDir: string) {
    this.persistPath = path.join(workingDir, 'knowledge_graph.json');
  }

  async upsertNode(nodeId: string, nodeData: NodeData): Promise<void> {
    this.nodes.set(nodeId, nodeData);
    if (!this.edges.has(nodeId)) {
      this.edges.set(nodeId, new Map());
    }
    await this.persist();
  }

  async upsertEdge(sourceId: string, targetId: string, edgeData: any): Promise<void> {
    if (!this.edges.has(sourceId)) {
      this.edges.set(sourceId, new Map());
    }
    if (!this.edges.has(targetId)) {
      this.edges.set(targetId, new Map());
    }
    
    this.edges.get(sourceId)!.set(targetId, edgeData);
    await this.persist();
  }

  async getNode(nodeId: string): Promise<NodeData | null> {
    return this.nodes.get(nodeId) || null;
  }

  async getNeighbors(nodeId: string): Promise<string[]> {
    const neighbors = this.edges.get(nodeId);
    return neighbors ? Array.from(neighbors.keys()) : [];
  }

  async removeNode(nodeId: string): Promise<void> {
    this.nodes.delete(nodeId);
    this.edges.delete(nodeId);
    
    // 删除指向该节点的边
    for (const [_, neighbors] of this.edges.entries()) {
      neighbors.delete(nodeId);
    }
    await this.persist();
  }

  async removeEdge(sourceId: string, targetId: string): Promise<void> {
    const neighbors = this.edges.get(sourceId);
    if (neighbors) {
      neighbors.delete(targetId);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.nodes = new Map(Object.entries(data.nodes || {}));
        
        // 重建边的Map结构
        this.edges = new Map();
        for (const [sourceId, targets] of Object.entries(data.edges || {})) {
          this.edges.set(sourceId, new Map(Object.entries(targets as any)));
        }
      }
    } catch (error) {
      logger.warn('Failed to load knowledge graph:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = {
        nodes: Object.fromEntries(this.nodes),
        edges: Object.fromEntries(
          Array.from(this.edges.entries()).map(([key, value]) => [
            key,
            Object.fromEntries(value)
          ])
        ),
        timestamp: Date.now()
      };
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist knowledge graph:', error);
    }
  }
}

/**
 * LightRAG兼容的RAG后端实现
 */
export class LightRAGBackend implements RAGBackend {
  private workingDir: string;
  private textChunks: LightRAGTextChunkStorage;
  private chunksVdb: LightRAGVectorDatabase;
  private entitiesVdb: LightRAGVectorDatabase;
  private relationshipsVdb: LightRAGVectorDatabase;
  private knowledgeGraph: LightRAGKnowledgeGraph;
  private llmModelFunc: LLMModelFunction | undefined;
  private embeddingFunc: EmbeddingFunction | undefined;
  private initialized: boolean = false;

  constructor(
    workingDir: string,
    embeddingFunc?: EmbeddingFunction,
    llmModelFunc?: LLMModelFunction
  ) {
    this.workingDir = workingDir;
    this.embeddingFunc = embeddingFunc;
    this.llmModelFunc = llmModelFunc;
    
    // 初始化存储组件
    this.textChunks = new LightRAGTextChunkStorage(workingDir);
    this.chunksVdb = new LightRAGVectorDatabase(path.join(workingDir, 'chunks'), embeddingFunc);
    this.entitiesVdb = new LightRAGVectorDatabase(path.join(workingDir, 'entities'), embeddingFunc);
    this.relationshipsVdb = new LightRAGVectorDatabase(path.join(workingDir, 'relationships'), embeddingFunc);
    this.knowledgeGraph = new LightRAGKnowledgeGraph(workingDir);
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 确保工作目录存在
      await fs.ensureDir(this.workingDir);
      await fs.ensureDir(path.join(this.workingDir, 'chunks'));
      await fs.ensureDir(path.join(this.workingDir, 'entities'));
      await fs.ensureDir(path.join(this.workingDir, 'relationships'));
      
      // 加载已存在的数据
      await Promise.all([
        this.textChunks.load(),
        this.chunksVdb.load(),
        this.entitiesVdb.load(),
        this.relationshipsVdb.load(),
        this.knowledgeGraph.load()
      ]);
      
      this.initialized = true;
      logger.info('LightRAGBackend initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize LightRAGBackend:', error);
      throw error;
    }
  }

  // 实现其他RAGBackend接口方法...
  async insertTextContent(
    content: string,
    filePath: string,
    options: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
      docId?: string;
    } = {}
  ): Promise<void> {
    try {
      logger.info(`Inserting text content from ${filePath}`);

      // 步骤1: 文本分块
      const chunks = this.splitTextContent(content, options);

      // 步骤2: 为每个文本块创建存储记录
      const chunkIds: string[] = [];
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = options.docId ? `${options.docId}_chunk_${i}` : calculateStringHash(chunk, 'chunk-');

        const chunkData: ChunkData = {
          tokens: this.estimateTokens(chunk),
          content: chunk,
          chunk_order_index: i,
          full_doc_id: options.docId || calculateStringHash(content, 'doc-'),
          file_path: filePath
        };

        // 存储文本块
        await this.textChunks.upsert({ [chunkId]: chunkData });

        // 添加到向量数据库
        await this.chunksVdb.upsert({
          [chunkId]: {
            content: chunk,
            file_path: filePath,
            chunk_order_index: i,
            type: 'text'
          }
        });

        chunkIds.push(chunkId);
      }

      // 步骤3: 提取实体和关系（模拟LightRAG的extract_entities）
      await this.extractEntitiesAndRelationships(chunks, chunkIds, filePath);

      logger.info(`Successfully inserted ${chunks.length} text chunks from ${filePath}`);

    } catch (error) {
      logger.error('Failed to insert text content:', error);
      throw error;
    }
  }

  async createEntity(entityInfo: EntityInfo, chunkId: string): Promise<void> {
    // 实现实体创建逻辑
    logger.info(`Creating entity: ${entityInfo.entity_name}`);
    // TODO: 实现完整的实体创建逻辑
  }

  async updateEntity(entityId: string, updates: Partial<EntityInfo>): Promise<void> {
    // 实现实体更新逻辑
    logger.info(`Updating entity: ${entityId}`);
  }

  async getEntity(entityId: string): Promise<EntityInfo | null> {
    // 实现实体获取逻辑
    return null;
  }

  async createRelationship(
    sourceEntityId: string,
    targetEntityId: string,
    relationshipType: string,
    description: string,
    metadata?: any
  ): Promise<void> {
    // 实现关系创建逻辑
    logger.info(`Creating relationship: ${sourceEntityId} -> ${targetEntityId} (${relationshipType})`);
  }

  async createTextChunk(chunkData: ChunkData): Promise<string> {
    // 实现文本块创建逻辑
    const chunkId = calculateStringHash(chunkData.content, 'chunk-');
    await this.textChunks.upsert({ [chunkId]: chunkData });
    return chunkId;
  }

  async getTextChunk(chunkId: string): Promise<ChunkData | null> {
    return await this.textChunks.get(chunkId);
  }

  async query(query: string, params: QueryParams = {}): Promise<QueryResult> {
    // 实现查询逻辑，模拟LightRAG的aquery方法
    logger.info(`Querying: ${query}`);
    
    // TODO: 实现完整的查询逻辑
    return {
      answer: `LightRAG兼容查询结果: ${query}`,
      sources: [],
      metadata: { mode: params.mode || 'hybrid' }
    };
  }

  async cleanup(): Promise<void> {
    logger.info('LightRAGBackend cleanup completed');
  }

  /**
   * 分割文本内容（模拟LightRAG的文本分块策略）
   */
  private splitTextContent(
    content: string,
    options: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
    }
  ): string[] {
    const { splitByCharacter, splitByCharacterOnly = false } = options;

    if (splitByCharacter) {
      const chunks = content.split(splitByCharacter);
      if (splitByCharacterOnly) {
        return chunks.filter(chunk => chunk.trim().length > 0);
      }
    }

    // 默认分块策略：按段落和长度分割
    const paragraphs = content.split(/\n\s*\n/);
    const chunks: string[] = [];
    let currentChunk = '';
    const maxChunkSize = 1000; // 字符数

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * 提取实体和关系（模拟LightRAG的extract_entities功能）
   */
  private async extractEntitiesAndRelationships(
    chunks: string[],
    chunkIds: string[],
    filePath: string
  ): Promise<void> {
    if (!this.llmModelFunc) {
      logger.warn('No LLM function available for entity extraction');
      return;
    }

    try {
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = chunkIds[i];

        // 提取实体
        const entities = await this.extractEntitiesFromChunk(chunk, chunkId, filePath);

        // 提取关系
        await this.extractRelationshipsFromEntities(entities, chunk, chunkId, filePath);
      }

    } catch (error) {
      logger.error('Failed to extract entities and relationships:', error);
    }
  }

  /**
   * 从文本块中提取实体
   */
  private async extractEntitiesFromChunk(
    chunk: string,
    chunkId: string,
    filePath: string
  ): Promise<EntityInfo[]> {
    try {
      const extractionPrompt = `
分析以下文本，提取其中的所有重要实体（人物、地点、组织、概念、事件等）：

文本内容：
${chunk}

请以JSON格式返回提取的实体：
{
  "entities": [
    {
      "name": "实体名称",
      "type": "实体类型（Person/Location/Organization/Concept/Event等）",
      "description": "实体的详细描述"
    }
  ]
}
`;

      const response = await this.llmModelFunc(extractionPrompt);
      const extractedData = this.parseEntityExtractionResponse(response);

      const entities: EntityInfo[] = [];

      for (const entityData of extractedData.entities) {
        const entity: EntityInfo = {
          entity_name: entityData.name,
          entity_type: entityData.type,
          summary: entityData.description,
          source_id: chunkId,
          file_path: filePath,
          created_at: Date.now()
        };

        // 创建实体
        await this.createEntity(entity, chunkId);
        entities.push(entity);
      }

      return entities;

    } catch (error) {
      logger.warn('Failed to extract entities from chunk:', error);
      return [];
    }
  }

  /**
   * 从实体中提取关系
   */
  private async extractRelationshipsFromEntities(
    entities: EntityInfo[],
    chunk: string,
    chunkId: string,
    filePath: string
  ): Promise<void> {
    if (entities.length < 2) return;

    try {
      // 构建关系提取提示词
      const entityNames = entities.map(e => e.entity_name);
      const relationshipPrompt = `
分析以下文本中实体之间的关系：

文本内容：
${chunk}

实体列表：
${entityNames.join(', ')}

请识别这些实体之间的关系，以JSON格式返回：
{
  "relationships": [
    {
      "source": "源实体名称",
      "target": "目标实体名称",
      "type": "关系类型（如：works_at, located_in, part_of, related_to等）",
      "description": "关系描述"
    }
  ]
}
`;

      const response = await this.llmModelFunc(relationshipPrompt);
      const relationshipData = this.parseRelationshipExtractionResponse(response);

      // 创建关系
      for (const rel of relationshipData.relationships) {
        try {
          await this.createRelationship(
            rel.source,
            rel.target,
            rel.type,
            rel.description,
            {
              source_chunk: chunkId,
              file_path: filePath,
              auto_extracted: true,
              weight: 1.0
            }
          );
        } catch (error) {
          logger.warn(`Failed to create relationship ${rel.source} -> ${rel.target}:`, error);
        }
      }

    } catch (error) {
      logger.warn('Failed to extract relationships:', error);
    }
  }

  /**
   * 解析实体提取响应
   */
  private parseEntityExtractionResponse(response: string): { entities: Array<{ name: string; type: string; description: string }> } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          entities: parsed.entities || []
        };
      }
    } catch (error) {
      logger.warn('Failed to parse entity extraction response:', error);
    }

    return { entities: [] };
  }

  /**
   * 解析关系提取响应
   */
  private parseRelationshipExtractionResponse(response: string): { relationships: Array<{ source: string; target: string; type: string; description: string }> } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          relationships: parsed.relationships || []
        };
      }
    } catch (error) {
      logger.warn('Failed to parse relationship extraction response:', error);
    }

    return { relationships: [] };
  }

  /**
   * 估算token数量
   */
  private estimateTokens(text: string): number {
    // 简单的token估算：平均每个token约4个字符
    return Math.ceil(text.length / 4);
  }
}
