/**
 * RAGAnything核心类 - TypeScript实现
 * 
 * 多模态文档处理管道 - 完整的文档解析和插入管道
 */

import path from 'path';
import fs from 'fs-extra';
import {
  RAGAnythingConfig,
  ProcessOptions,
  BatchProcessOptions,
  QueryParams,
  QueryResult,
  MultimodalContent,
  ContentBlock,
  ParseResult,
  EntityInfo,
  LLMModelFunction,
  VisionModelFunction,
  EmbeddingFunction,
  ContentType,
  ProcessError,
  RAGError,
  RAGBackend
} from '@/types';
import { MineruParser } from '@/parsers';
import { 
  ImageModalProcessor,
  TableModalProcessor,
  EquationModalProcessor,
  GenericModalProcessor,
  BaseModalProcessor
} from '@/processors';
import {
  ensureDir,
  getFilesInDirectory,
  isSupportedFileFormat,
  getFileCategory
} from '@/utils/fileUtils';
import { getModuleLogger } from '@/utils/logger';
import { DefaultRAGBackend } from './DefaultRAGBackend';
import { LightRAGBackend } from './LightRAGBackend';

const logger = getModuleLogger('RAGAnything');

/**
 * RAGAnything主类
 */
export class RAGAnything {
  private workingDir: string;
  private llmModelFunc: LLMModelFunction | undefined;
  private visionModelFunc: VisionModelFunction | undefined;
  private embeddingFunc: EmbeddingFunction | undefined;
  private modalProcessors: Map<ContentType, BaseModalProcessor>;
  private ragBackend: RAGBackend;
  private initialized: boolean = false;

  constructor(config: RAGAnythingConfig = {}) {
    this.workingDir = config.workingDir || './rag_storage';
    this.llmModelFunc = config.llmModelFunc;
    this.visionModelFunc = config.visionModelFunc;
    this.embeddingFunc = config.embeddingFunc;
    this.modalProcessors = new Map();

    // 初始化 RAG 后端（优先使用LightRAG兼容后端）
    this.ragBackend = config.ragBackend || new LightRAGBackend(
      this.workingDir,
      this.embeddingFunc,
      this.llmModelFunc
    );

    logger.info('RAGAnything initialized', {
      workingDir: this.workingDir,
      hasLLMFunc: !!this.llmModelFunc,
      hasVisionFunc: !!this.visionModelFunc,
      hasEmbeddingFunc: !!this.embeddingFunc,
      ragBackendType: this.ragBackend.constructor.name
    });
  }

  /**
   * 初始化RAGAnything实例
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 创建工作目录
      await ensureDir(this.workingDir);

      // 检查MinerU安装
      if (!await MineruParser.checkInstallation()) {
        throw new RAGError(
          'MinerU 2.0 is not properly installed. ' +
          'Please install it using: npm install mineru or pip install mineru[core]'
        );
      }

      // 验证必需的函数
      if (!this.llmModelFunc) {
        throw new RAGError('llm_model_func is required for RAGAnything initialization');
      }

      if (!this.embeddingFunc) {
        throw new RAGError('embedding_func is required for RAGAnything initialization');
      }

      // 初始化 RAG 后端
      await this.ragBackend.initialize();

      // 初始化处理器
      await this.initializeProcessors();

      this.initialized = true;
      logger.info('RAGAnything initialization completed');

    } catch (error) {
      logger.error('Failed to initialize RAGAnything:', error);
      throw error;
    }
  }

  /**
   * 确保RAG系统已初始化（模拟LightRAG的自动初始化）
   */
  private async ensureRAGInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 初始化多模态处理器
   */
  private async initializeProcessors(): Promise<void> {
    if (!this.llmModelFunc) {
      throw new RAGError('LLM model function is required for processor initialization');
    }

    try {
      // 创建图像处理器
      this.modalProcessors.set('image', new ImageModalProcessor({
        modalCaptionFunc: this.visionModelFunc || this.llmModelFunc,
        maxRetries: 3,
        timeout: 30000,
        ragBackend: this.ragBackend
      }));

      // 创建表格处理器
      this.modalProcessors.set('table', new TableModalProcessor({
        modalCaptionFunc: this.llmModelFunc,
        maxRetries: 3,
        timeout: 30000,
        ragBackend: this.ragBackend
      }));

      // 创建公式处理器
      this.modalProcessors.set('equation', new EquationModalProcessor({
        modalCaptionFunc: this.llmModelFunc,
        maxRetries: 3,
        timeout: 30000,
        ragBackend: this.ragBackend
      }));

      // 创建通用处理器
      this.modalProcessors.set('generic', new GenericModalProcessor({
        modalCaptionFunc: this.llmModelFunc,
        maxRetries: 3,
        timeout: 30000,
        ragBackend: this.ragBackend
      }));

      logger.info('Modal processors initialized', {
        processors: Array.from(this.modalProcessors.keys())
      });

    } catch (error) {
      logger.error('Failed to initialize processors:', error);
      throw new RAGError(`Failed to initialize processors: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 解析文档
   */
  async parseDocument(
    filePath: string,
    options: ProcessOptions = {}
  ): Promise<ParseResult> {
    const {
      outputDir = './output',
      parseMethod = 'auto',
      displayStats = true
    } = options;

    logger.info(`Starting document parsing: ${filePath}`);

    try {
      if (!await fs.pathExists(filePath)) {
        throw new ProcessError(`File not found: ${filePath}`, 'file_not_found');
      }

      if (!isSupportedFileFormat(filePath)) {
        throw new ProcessError(`Unsupported file format: ${filePath}`, 'unsupported_format');
      }

      // 使用MinerU解析文档
      const result = await MineruParser.parseDocument(filePath, outputDir, {
        method: parseMethod
      });

      if (displayStats && result.metadata) {
        logger.info('Document parsing completed', {
          totalBlocks: result.metadata.totalBlocks,
          blockTypes: result.metadata.blockTypes,
          textLength: result.metadata.textLength
        });
      }

      return result;

    } catch (error) {
      logger.error(`Failed to parse document: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 分离文本和多模态内容
   */
  private separateContent(contentList: ContentBlock[]): {
    textContent: string;
    multimodalItems: MultimodalContent[];
  } {
    const textParts: string[] = [];
    const multimodalItems: MultimodalContent[] = [];

    for (const item of contentList) {
      if (item.type === 'text' && item.text) {
        textParts.push(item.text);
      } else if (item.type !== 'text') {
        multimodalItems.push(item as MultimodalContent);
      }
    }

    const textContent = textParts.join('\n\n');

    logger.info('Content separation completed', {
      textLength: textContent.length,
      multimodalCount: multimodalItems.length,
      multimodalTypes: this.countMultimodalTypes(multimodalItems)
    });

    return { textContent, multimodalItems };
  }

  /**
   * 统计多模态内容类型
   */
  private countMultimodalTypes(items: MultimodalContent[]): Record<string, number> {
    const counts: Record<string, number> = {};
    
    for (const item of items) {
      counts[item.type] = (counts[item.type] || 0) + 1;
    }
    
    return counts;
  }

  /**
   * 插入文本内容（公共方法）
   */
  async insertTextContent(
    textContent: string,
    filePath: string,
    options: ProcessOptions = {}
  ): Promise<void> {
    await this.ensureInitialized();
    return this._insertTextContent(textContent, filePath, options);
  }

  /**
   * 插入文本内容（私有实现）
   */
  private async _insertTextContent(
    textContent: string,
    filePath: string,
    options: ProcessOptions = {}
  ): Promise<void> {
    const {
      splitByCharacter,
      splitByCharacterOnly = false,
      docId
    } = options;

    if (!textContent.trim()) {
      logger.debug('No text content to insert');
      return;
    }

    logger.info('Inserting text content into RAG system', {
      textLength: textContent.length,
      filePath: path.basename(filePath)
    });

    try {
      // 使用 RAG 后端插入文本内容
      const insertOptions: any = { splitByCharacterOnly };
      if (splitByCharacter) {
        insertOptions.splitByCharacter = splitByCharacter;
      }
      if (docId) {
        insertOptions.docId = docId;
      }

      await this.ragBackend.insertTextContent(textContent, filePath, insertOptions);

      logger.info('Text content insertion completed');

    } catch (error) {
      logger.error('Failed to insert text content:', error);
      throw new ProcessError(
        `Failed to insert text content: ${error instanceof Error ? error.message : String(error)}`,
        'text_insertion'
      );
    }
  }

  /**
   * 处理多模态内容
   */
  private async processMultimodalContent(
    multimodalItems: MultimodalContent[],
    filePath: string
  ): Promise<void> {
    if (multimodalItems.length === 0) {
      logger.debug('No multimodal content to process');
      return;
    }

    logger.info(`Processing ${multimodalItems.length} multimodal items`);

    const fileName = path.basename(filePath);

    for (let i = 0; i < multimodalItems.length; i++) {
      const item = multimodalItems[i];

      if (!item) {
        logger.warn(`Skipping undefined item at index ${i}`);
        continue;
      }

      try {
        logger.info(`Processing item ${i + 1}/${multimodalItems.length}: ${item.type}`);

        const processor = this.getProcessorForType(item.type);

        if (processor) {
          const [enhancedCaption, entityInfo] = await processor.processMultimodalContent(
            item,
            item.type,
            fileName
          );

          logger.info(`${item.type} processing completed: ${entityInfo.entity_name}`);
        } else {
          logger.warn(`No processor found for content type: ${item.type}`);
        }

      } catch (error) {
        logger.error(`Failed to process multimodal item ${i + 1}:`, error);
        // 继续处理其他项目
      }
    }

    logger.info('Multimodal content processing completed');
  }

  /**
   * 获取内容类型对应的处理器
   */
  private getProcessorForType(contentType: ContentType): BaseModalProcessor | undefined {
    // 直接映射
    if (this.modalProcessors.has(contentType)) {
      return this.modalProcessors.get(contentType);
    }

    // 对于未知类型，使用通用处理器
    return this.modalProcessors.get('generic');
  }

  /**
   * 完整的文档处理工作流
   */
  async processDocumentComplete(
    filePath: string,
    options: ProcessOptions = {}
  ): Promise<void> {
    // 确保RAG系统已初始化（模拟LightRAG的自动初始化）
    await this.ensureRAGInitialized();

    logger.info(`Starting complete document processing: ${filePath}`);

    try {
      // 步骤1: 解析文档
      const parseResult = await this.parseDocument(filePath, options);

      // 步骤2: 分离文本和多模态内容
      const { textContent, multimodalItems } = this.separateContent(parseResult.contentList);

      // 步骤3: 插入文本内容（使用LightRAG风格的参数）
      if (textContent.trim()) {
        await this.insertTextContentLightRAGStyle(
          textContent,
          filePath,
          options.splitByCharacter,
          options.splitByCharacterOnly || false,
          options.docId
        );
      }

      // 步骤4: 处理多模态内容
      if (multimodalItems.length > 0) {
        await this.processMultimodalContent(multimodalItems, filePath);
      }

      logger.info(`Document processing completed: ${filePath}`);

    } catch (error) {
      logger.error(`Failed to process document: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * LightRAG风格的文本内容插入
   */
  private async insertTextContentLightRAGStyle(
    content: string,
    filePath: string,
    splitByCharacter?: string,
    splitByCharacterOnly: boolean = false,
    docId?: string
  ): Promise<void> {
    logger.info('Starting text content insertion into RAG system...');

    try {
      // 使用RAG后端的插入方法（模拟LightRAG的ainsert）
      const insertOptions: any = {
        splitByCharacterOnly,
        file_paths: path.basename(filePath)
      };

      if (splitByCharacter) {
        insertOptions.splitByCharacter = splitByCharacter;
      }
      if (docId) {
        insertOptions.docId = docId;
      }

      await this.ragBackend.insertTextContent(content, filePath, insertOptions);

      logger.info('Text content insertion complete');

    } catch (error) {
      logger.error('Failed to insert text content:', error);
      throw new ProcessError(
        `Failed to insert text content: ${error instanceof Error ? error.message : String(error)}`,
        'text_insertion'
      );
    }
  }

  /**
   * 批量处理文件夹中的文档
   */
  async processFolderComplete(
    folderPath: string,
    options: BatchProcessOptions = {}
  ): Promise<{
    total: number;
    success: number;
    failed: number;
    failedFiles: Array<{ filePath: string; error: string }>;
  }> {
    await this.ensureInitialized();

    const {
      outputDir = './output',
      fileExtensions,
      recursive = true,
      maxWorkers = 1,
      ...processOptions
    } = options;

    logger.info(`Starting batch processing: ${folderPath}`);

    try {
      if (!await fs.pathExists(folderPath)) {
        throw new ProcessError(`Folder not found: ${folderPath}`, 'folder_not_found');
      }

      // 收集要处理的文件
      const allFiles = await getFilesInDirectory(folderPath, fileExtensions, recursive);
      const filesToProcess = allFiles.filter(file => isSupportedFileFormat(file));

      if (filesToProcess.length === 0) {
        logger.info(`No supported files found in ${folderPath}`);
        return { total: 0, success: 0, failed: 0, failedFiles: [] };
      }

      logger.info(`Found ${filesToProcess.length} files to process`);

      // 统计文件类型
      const fileTypeCount = this.countFileTypes(filesToProcess);
      logger.info('File type distribution:', fileTypeCount);

      // 处理文件
      const results = await this.processFilesInBatches(
        filesToProcess,
        outputDir,
        processOptions,
        maxWorkers
      );

      logger.info('Batch processing completed', results);
      return results;

    } catch (error) {
      logger.error(`Failed to process folder: ${folderPath}`, error);
      throw error;
    }
  }

  /**
   * 分批处理文件
   */
  private async processFilesInBatches(
    files: string[],
    outputDir: string,
    processOptions: ProcessOptions,
    maxWorkers: number
  ): Promise<{
    total: number;
    success: number;
    failed: number;
    failedFiles: Array<{ filePath: string; error: string }>;
  }> {
    const results = {
      total: files.length,
      success: 0,
      failed: 0,
      failedFiles: [] as Array<{ filePath: string; error: string }>
    };

    // 创建处理任务
    const processingTasks = files.map(async (filePath, index) => {
      try {
        logger.info(`[${index + 1}/${files.length}] Processing: ${filePath}`);

        // 为每个文件创建单独的输出目录
        const fileOutputDir = path.join(outputDir, path.basename(filePath, path.extname(filePath)));
        await ensureDir(fileOutputDir);

        await this.processDocumentComplete(filePath, {
          ...processOptions,
          outputDir: fileOutputDir
        });

        results.success++;
        logger.info(`[${index + 1}/${files.length}] Successfully processed: ${filePath}`);

      } catch (error) {
        results.failed++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        results.failedFiles.push({ filePath, error: errorMessage });

        logger.error(`[${index + 1}/${files.length}] Failed to process: ${filePath}`, error);
      }
    });

    // 控制并发数量
    if (maxWorkers === 1) {
      // 顺序处理
      for (const task of processingTasks) {
        await task;
      }
    } else {
      // 并发处理
      const chunks = this.chunkArray(processingTasks, maxWorkers);
      for (const chunk of chunks) {
        await Promise.all(chunk);
      }
    }

    return results;
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 统计文件类型
   */
  private countFileTypes(files: string[]): Record<string, number> {
    const counts: Record<string, number> = {};

    for (const file of files) {
      const ext = path.extname(file).toLowerCase();
      counts[ext] = (counts[ext] || 0) + 1;
    }

    return counts;
  }

  /**
   * 多模态查询
   */
  async queryWithMultimodal(
    query: string,
    params: QueryParams = {}
  ): Promise<QueryResult> {
    await this.ensureInitialized();

    const { mode = 'hybrid' } = params;

    logger.info(`Processing multimodal query: ${query.substring(0, 100)}...`, { mode });

    try {
      // 使用 RAG 后端进行查询
      const result = await this.ragBackend.query(query, params);

      logger.info('Multimodal query completed', {
        answerLength: result.answer.length,
        sourcesCount: result.sources?.length || 0
      });

      return result;

    } catch (error) {
      logger.error('Failed to process multimodal query:', error);
      throw new ProcessError(
        `Failed to process query: ${error instanceof Error ? error.message : String(error)}`,
        'query_processing'
      );
    }
  }

  /**
   * 检查MinerU安装状态
   */
  async checkMineruInstallation(): Promise<boolean> {
    return await MineruParser.checkInstallation();
  }

  /**
   * 获取处理器信息
   */
  getProcessorInfo(): {
    status: string;
    mineruInstalled: boolean;
    processors: Record<string, any>;
    models: Record<string, string>;
  } {
    const info = {
      status: this.initialized ? 'Initialized' : 'Not initialized',
      mineruInstalled: false, // 这里应该异步检查，但为了简化返回false
      processors: {} as Record<string, any>,
      models: {
        llmModel: this.llmModelFunc ? 'External function' : 'Not provided',
        visionModel: this.visionModelFunc ? 'External function' : 'Not provided',
        embeddingModel: this.embeddingFunc ? 'External function' : 'Not provided'
      }
    };

    // 获取处理器信息
    for (const [type, processor] of this.modalProcessors.entries()) {
      info.processors[type] = {
        class: processor.constructor.name,
        supports: this.getProcessorSupports(type)
      };
    }

    return info;
  }

  /**
   * 获取处理器支持的功能
   */
  private getProcessorSupports(processorType: ContentType): string[] {
    const supportsMap: Record<ContentType, string[]> = {
      image: [
        'Image content analysis',
        'Visual understanding',
        'Image description generation',
        'Image entity extraction'
      ],
      table: [
        'Table structure analysis',
        'Data statistics',
        'Trend identification',
        'Table entity extraction'
      ],
      equation: [
        'Mathematical formula parsing',
        'Variable identification',
        'Formula meaning explanation',
        'Formula entity extraction'
      ],
      text: [
        'Text content processing',
        'Text entity extraction'
      ],
      generic: [
        'General content analysis',
        'Structured processing',
        'Entity extraction'
      ]
    };

    return supportsMap[processorType] || ['Basic processing'];
  }

  /**
   * 确保实例已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 获取支持的文件格式
   */
  static getSupportedFormats(): string[] {
    return [
      '.pdf',
      '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp',
      '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
      '.txt', '.md'
    ];
  }

  /**
   * 检查文件是否为支持的格式
   */
  static isSupportedFormat(filePath: string): boolean {
    return isSupportedFileFormat(filePath);
  }

  /**
   * 获取工作目录
   */
  getWorkingDir(): string {
    return this.workingDir;
  }

  /**
   * 获取初始化状态
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    logger.info('Cleaning up RAGAnything resources');

    try {
      // 清理RAG后端
      if (this.ragBackend) {
        await this.ragBackend.cleanup();
      }

      // 清理处理器
      this.modalProcessors.clear();

      // 重置状态
      this.initialized = false;

      logger.info('RAGAnything cleanup completed');

    } catch (error) {
      logger.error('Error during cleanup:', error);
      throw error;
    }
  }
}
