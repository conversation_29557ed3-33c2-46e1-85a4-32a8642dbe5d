# RAG-Anything TypeScript

🚀 **RAG-Anything的TypeScript实现** - 全能多模态RAG系统

## 概述

这是RAG-Anything项目的TypeScript重构版本，提供了与原Python版本相同的功能，但具有更好的类型安全性和现代JavaScript生态系统的支持。

## 主要特性

- 🔄 **端到端多模态管道** - 从文档摄取解析到智能多模态查询回答的完整工作流
- 📄 **通用文档支持** - 无缝处理PDF、Office文档、图像和各种文件格式
- 🧠 **专业内容分析** - 针对图像、表格、数学公式和异构内容类型的专用处理器
- 🔗 **多模态知识图谱** - 自动实体提取和跨模态关系发现，增强理解能力
- ⚡ **自适应处理模式** - 灵活的MinerU解析或直接多模态内容注入工作流
- 🎯 **混合智能检索** - 跨文本和多模态内容的高级搜索功能，具有上下文理解

## 技术栈

- **TypeScript** - 类型安全的JavaScript超集
- **Node.js** - 服务器端JavaScript运行时
- **Sharp** - 高性能图像处理
- **PDF-Parse** - PDF文档解析
- **Mammoth** - Word文档处理
- **XLSX** - Excel文档处理

## 快速开始

### 安装依赖

```bash
npm install
```

### 构建项目

```bash
npm run build
```

### 运行示例

```bash
npm run dev
```

### 运行测试

```bash
npm test
```

## 项目结构

```
src/
├── types/          # TypeScript类型定义
├── utils/          # 工具函数
├── parsers/        # 文档解析器
├── processors/     # 多模态处理器
├── core/           # 核心RAG功能
├── examples/       # 使用示例
└── test/           # 测试文件
```

## 安装

```bash
npm install rag-anything-ts
```

## 快速开始

### 1. 基础使用

```typescript
import { RAGAnything } from 'rag-anything-ts';

// 定义你的模型函数
const llmFunction = async (prompt: string, systemPrompt?: string) => {
  // 调用你的LLM API (OpenAI, Claude等)
  return "LLM响应";
};

const visionFunction = async (prompt: string, systemPrompt?: string, historyMessages?: any[], imageData?: string) => {
  // 调用你的视觉模型API
  return "视觉模型响应";
};

const embeddingFunction = async (texts: string[]) => {
  // 调用你的嵌入模型API
  return texts.map(() => Array.from({length: 1536}, () => Math.random()));
};

// 创建RAGAnything实例
const rag = new RAGAnything({
  workingDir: './rag_storage',
  llmModelFunc: llmFunction,
  visionModelFunc: visionFunction,
  embeddingFunc: embeddingFunction,
});

// 初始化
await rag.initialize();

// 处理文档
await rag.processDocumentComplete('path/to/document.pdf');

// 查询
const result = await rag.queryWithMultimodal('你的问题', { mode: 'hybrid' });
console.log(result.answer);
```

### 2. 使用OpenAI

```typescript
import {
  RAGAnything,
  DefaultRAGBackend,
  createOpenAILLMFunction,
  createOpenAIVisionFunction,
  createOpenAIEmbeddingFunction,
  createOpenAIConfig
} from 'rag-anything-ts';

// 创建OpenAI配置
const openaiConfig = createOpenAIConfig(process.env.OPENAI_API_KEY!, {
  model: 'gpt-4o-mini',
  visionModel: 'gpt-4o',
  embeddingModel: 'text-embedding-3-large'
});

// 创建模型函数
const llmFunc = createOpenAILLMFunction(openaiConfig);
const visionFunc = createOpenAIVisionFunction(openaiConfig);
const embeddingFunc = createOpenAIEmbeddingFunction(openaiConfig);

// 创建RAG实例（会自动创建默认RAG后端）
const rag = new RAGAnything({
  workingDir: './rag_storage',
  llmModelFunc: llmFunc,
  visionModelFunc: visionFunc,
  embeddingFunc: embeddingFunc
});

await rag.initialize();
await rag.processDocumentComplete('document.pdf');
const result = await rag.queryWithMultimodal('总结文档内容');
console.log(result.answer);
```

### 3. 批量处理

```typescript
// 批量处理文件夹中的文档
const batchResult = await rag.processFolderComplete('./documents', {
  outputDir: './output',
  fileExtensions: ['.pdf', '.docx', '.jpg', '.png'],
  recursive: true,
  maxWorkers: 2
});

console.log(`处理完成: ${batchResult.success}/${batchResult.total} 个文件`);
```

## API文档

### RAGAnything类

#### 构造函数
```typescript
new RAGAnything(config: RAGAnythingConfig)
```

#### 主要方法

- `initialize()`: 初始化RAG系统和后端存储
- `processDocumentComplete(filePath, options)`: 处理单个文档，包括解析、实体提取和存储
- `processFolderComplete(folderPath, options)`: 批量处理文档
- `queryWithMultimodal(query, params)`: 执行多模态查询，支持混合检索
- `insertTextContent(content, filePath, options)`: 直接插入文本内容
- `checkMineruInstallation()`: 检查MinerU安装状态
- `getProcessorInfo()`: 获取处理器信息和状态
- `cleanup()`: 清理资源和持久化数据

### 支持的文件格式

- **PDF文档**: `.pdf`
- **图像文件**: `.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.gif`, `.webp`
- **Office文档**: `.doc`, `.docx`, `.ppt`, `.pptx`, `.xls`, `.xlsx`
- **文本文件**: `.txt`, `.md`

### 处理器类型

- **ImageModalProcessor**: 处理图像内容，生成描述和提取实体
- **TableModalProcessor**: 分析表格结构，提取数据洞察
- **EquationModalProcessor**: 解析数学公式，解释含义
- **GenericModalProcessor**: 处理通用和自定义内容类型

### RAG后端

- **DefaultRAGBackend**: 内置的RAG后端实现
  - 内存向量数据库，支持语义搜索
  - 知识图谱存储，自动提取实体关系
  - 数据持久化到本地文件
  - 自动实体关系提取和"belongs_to"关系构建

### 新增功能

✨ **完整的RAG功能**: 不再是模拟实现，提供真实的文档存储、向量化和查询功能

🔗 **实体关系提取**: 自动识别和构建实体间的关系，如"belongs_to"、"related_to"等

💾 **数据持久化**: 自动保存实体、文本块和关系到本地存储

🔍 **混合检索**: 支持向量相似度搜索和传统文本匹配

🤖 **智能答案生成**: 基于检索内容使用LLM生成准确答案

## 开发

### 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd rag-anything-ts

# 安装依赖
npm install

# 设置开发环境
npm run dev setup

# 复制环境变量模板
cp .env.example .env
# 编辑.env文件，填入你的API密钥
```

### 运行示例

```bash
# 基础示例
npm run dev example basic

# OpenAI集成示例
npm run dev example openai

# 处理器示例
npm run dev example processor

# 解析器示例
npm run dev example parser
```

### 运行测试

```bash
# 运行所有测试
npm test

# 运行特定测试
npm run dev test RAGAnything

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监视模式
npm run test:watch
```

### 代码检查

```bash
# 运行ESLint
npm run lint

# 自动修复
npm run lint:fix
```

### 构建

```bash
# 构建项目
npm run build

# 清理构建文件
npm run clean
```

## 系统要求

- **Node.js**: >= 18.0.0
- **MinerU**: 用于文档解析（可选，如果不安装将跳过相关功能）
- **LibreOffice**: 用于Office文档处理（可选）

### 安装MinerU

```bash
# 使用pip安装
pip install mineru[core]

# 或使用conda
conda install -c conda-forge mineru
```

## 配置选项

### RAGAnythingConfig

```typescript
interface RAGAnythingConfig {
  workingDir?: string;           // 工作目录，默认'./rag_storage'
  llmModelFunc?: LLMModelFunction;     // LLM模型函数
  visionModelFunc?: VisionModelFunction; // 视觉模型函数
  embeddingFunc?: EmbeddingFunction;   // 嵌入模型函数
}
```

### ProcessOptions

```typescript
interface ProcessOptions {
  outputDir?: string;           // 输出目录
  parseMethod?: 'auto' | 'ocr' | 'txt'; // 解析方法
  displayStats?: boolean;       // 显示统计信息
  splitByCharacter?: string;    // 分割字符
  docId?: string;              // 文档ID
}
```

### QueryParams

```typescript
interface QueryParams {
  mode?: 'local' | 'global' | 'hybrid'; // 查询模式
  topK?: number;               // 返回结果数量
  includeMetadata?: boolean;   // 包含元数据
}
```

## 故障排除

### 常见问题

1. **MinerU未安装**
   - 错误：`MinerU 2.0 is not properly installed`
   - 解决：安装MinerU或跳过需要MinerU的功能

2. **LibreOffice未安装**
   - 错误：`LibreOffice is required for Office document processing`
   - 解决：安装LibreOffice或只处理其他格式文件

3. **API密钥错误**
   - 错误：`API call failed`
   - 解决：检查环境变量中的API密钥是否正确

4. **内存不足**
   - 错误：处理大文件时内存溢出
   - 解决：减少并发数量或分批处理

### 调试

启用详细日志：

```typescript
import { setLogLevel } from 'rag-anything-ts';

setLogLevel('debug');
```

或设置环境变量：

```bash
export LOG_LEVEL=debug
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

### 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的多模态文档处理管道
- 支持PDF、图像、Office文档解析
- 专业的内容处理器
- OpenAI集成示例
- 完整的测试套件

## 致谢

- 原始Python版本：[RAG-Anything](https://github.com/HKUDS/RAG-Anything)
- MinerU文档解析引擎
- LightRAG知识图谱框架
