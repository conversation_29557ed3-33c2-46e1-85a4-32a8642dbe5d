/**
 * 基础多模态处理器
 */

import {
  BaseProcessor,
  ProcessorConfig,
  MultimodalContent,
  EntityInfo,
  ContentType,
  ChunkData,
  NodeData,
  LLMModelFunction,
  VisionModelFunction,
  ProcessError,
  RAGBackend
} from '@/types';
import { calculateStringHash } from '@/utils/fileUtils';
import { getModuleLogger } from '@/utils/logger';
import { buildPrompt } from '@/utils/prompts';

const logger = getModuleLogger('BaseModalProcessor');

/**
 * 基础多模态处理器抽象类
 */
export abstract class BaseModalProcessor implements BaseProcessor {
  protected modalCaptionFunc: LLMModelFunction | VisionModelFunction;
  protected maxRetries: number;
  protected timeout: number;
  protected ragBackend?: RAGBackend;

  constructor(config: ProcessorConfig) {
    this.modalCaptionFunc = config.modalCaptionFunc;
    this.maxRetries = config.maxRetries || 3;
    this.timeout = config.timeout || 30000;
    this.ragBackend = config.ragBackend;
  }

  /**
   * 处理多模态内容 - 抽象方法，子类必须实现
   */
  abstract processMultimodalContent(
    modalContent: MultimodalContent,
    contentType: ContentType,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]>;

  /**
   * 生成增强描述
   */
  protected async generateEnhancedDescription(
    content: MultimodalContent,
    promptKey: string
  ): Promise<string> {
    try {
      const variables = this.extractVariablesFromContent(content);
      const prompt = buildPrompt(promptKey as any, variables);
      
      const result = await this.callModelWithRetry(
        prompt.user,
        prompt.system,
        content
      );
      
      return result.trim();
    } catch (error) {
      logger.error('Failed to generate enhanced description:', error);
      throw new ProcessError(
        `Failed to generate enhanced description: ${error instanceof Error ? error.message : String(error)}`,
        content.type
      );
    }
  }

  /**
   * 提取实体信息
   */
  protected async extractEntityInfo(
    enhancedDescription: string,
    entityName: string,
    promptKey: string
  ): Promise<EntityInfo> {
    try {
      const variables = {
        enhanced_description: enhancedDescription,
        entity_name: entityName
      };
      
      const prompt = buildPrompt(promptKey as any, variables);
      
      const result = await this.callModelWithRetry(prompt.user, prompt.system);
      
      // 尝试解析JSON响应
      const entityInfo = this.parseEntityInfoFromResponse(result);
      
      return {
        entity_name: entityInfo.entity_name || entityName,
        entity_type: entityInfo.entity_type || 'unknown',
        summary: entityInfo.summary || enhancedDescription.substring(0, 200),
        created_at: Date.now()
      };
    } catch (error) {
      logger.error('Failed to extract entity info:', error);
      
      // 返回默认实体信息
      return {
        entity_name: entityName,
        entity_type: 'unknown',
        summary: enhancedDescription.substring(0, 200),
        created_at: Date.now()
      };
    }
  }

  /**
   * 从内容中提取变量
   */
  protected extractVariablesFromContent(content: MultimodalContent): Record<string, string> {
    const variables: Record<string, string> = {};
    
    switch (content.type) {
      case 'image':
        variables.image_description = content.img_path || '';
        variables.image_caption = content.img_caption?.join(', ') || '';
        variables.image_footnote = content.img_footnote?.join(', ') || '';
        break;
        
      case 'table':
        variables.table_content = content.table_body || '';
        variables.table_caption = content.table_caption?.join(', ') || '';
        variables.table_footnote = content.table_footnote?.join(', ') || '';
        break;
        
      case 'equation':
        variables.equation_latex = content.latex || '';
        variables.equation_caption = content.equation_caption?.join(', ') || '';
        variables.equation_footnote = content.equation_footnote?.join(', ') || '';
        break;
        
      case 'text':
        variables.content_data = content.text || '';
        variables.content_type = 'text';
        break;
        
      default:
        variables.content_data = JSON.stringify(content);
        variables.content_type = content.type;
    }
    
    return variables;
  }

  /**
   * 解析实体信息响应
   */
  protected parseEntityInfoFromResponse(response: string): Partial<EntityInfo> {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]) as Partial<EntityInfo>;
      }
      
      // 如果没有找到JSON，返回空对象
      return {};
    } catch (error) {
      logger.warn('Failed to parse entity info JSON:', error);
      return {};
    }
  }

  /**
   * 带重试的模型调用
   */
  protected async callModelWithRetry(
    prompt: string,
    systemPrompt?: string,
    content?: MultimodalContent,
    retries: number = this.maxRetries
  ): Promise<string> {
    for (let i = 0; i < retries; i++) {
      try {
        // 对于图像内容，如果有vision模型函数，使用它
        if (content?.type === 'image' && this.isVisionModelFunction(this.modalCaptionFunc)) {
          const imageData = await this.extractImageData(content);
          return await this.modalCaptionFunc(
            prompt,
            systemPrompt,
            [],
            imageData
          );
        } else {
          return await this.modalCaptionFunc(
            prompt,
            systemPrompt,
            []
          );
        }
      } catch (error) {
        logger.warn(`Model call attempt ${i + 1} failed:`, error);
        
        if (i === retries - 1) {
          throw error;
        }
        
        // 等待一段时间后重试
        await this.sleep(1000 * (i + 1));
      }
    }
    
    throw new Error('All retry attempts failed');
  }

  /**
   * 检查是否为视觉模型函数
   */
  protected isVisionModelFunction(func: any): func is VisionModelFunction {
    // 简单的类型检查，实际实现可能需要更复杂的逻辑
    return func.length >= 4; // VisionModelFunction有4个参数
  }

  /**
   * 从图像内容中提取图像数据
   */
  protected async extractImageData(content: MultimodalContent): Promise<string | undefined> {
    if (content.type !== 'image') {
      return undefined;
    }
    
    // 这里应该实现图像文件读取和base64编码
    // 由于这是一个简化版本，我们返回undefined
    // 实际实现需要读取img_path指向的文件并转换为base64
    logger.warn('Image data extraction not implemented in base class');
    return undefined;
  }

  /**
   * 创建实体和文本块
   */
  protected async createEntityAndChunk(
    modalChunk: string,
    entityInfo: EntityInfo,
    filePath: string
  ): Promise<[string, EntityInfo]> {
    try {
      // 创建块ID
      const chunkId = calculateStringHash(modalChunk, 'chunk-');

      // 如果有 RAG 后端，创建实体和文本块
      if (this.ragBackend) {
        // 创建文本块
        const chunkData: ChunkData = {
          tokens: this.estimateTokens(modalChunk),
          content: modalChunk,
          chunk_order_index: 0,
          full_doc_id: chunkId,
          file_path: filePath
        };

        await this.ragBackend.createTextChunk(chunkData);

        // 创建实体
        const finalEntityInfo: EntityInfo = {
          ...entityInfo,
          source_id: chunkId,
          file_path: filePath,
          created_at: Date.now()
        };

        await this.ragBackend.createEntity(finalEntityInfo, chunkId);

        logger.info(`Created entity: ${entityInfo.entity_name} with chunk: ${chunkId}`);

        return [modalChunk, finalEntityInfo];
      } else {
        // 如果没有 RAG 后端，只记录日志
        logger.info(`Created entity: ${entityInfo.entity_name} with chunk: ${chunkId} (no backend)`);
        return [modalChunk, entityInfo];
      }

    } catch (error) {
      logger.error('Failed to create entity and chunk:', error);
      throw new ProcessError(
        `Failed to create entity and chunk: ${error instanceof Error ? error.message : String(error)}`,
        entityInfo.entity_type
      );
    }
  }

  /**
   * 生成默认实体名称
   */
  protected generateDefaultEntityName(content: MultimodalContent, filePath?: string): string {
    const fileName = filePath ? filePath.split('/').pop()?.split('.')[0] || 'unknown' : 'unknown';
    const timestamp = Date.now();
    
    switch (content.type) {
      case 'image':
        return `Image_${fileName}_${timestamp}`;
      case 'table':
        return `Table_${fileName}_${timestamp}`;
      case 'equation':
        return `Equation_${fileName}_${timestamp}`;
      default:
        return `Content_${fileName}_${timestamp}`;
    }
  }

  /**
   * 睡眠函数
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证内容
   */
  protected validateContent(content: MultimodalContent): void {
    if (!content || typeof content !== 'object') {
      throw new ProcessError('Invalid content: content must be an object');
    }
    
    if (!content.type) {
      throw new ProcessError('Invalid content: type is required');
    }
  }

  /**
   * 获取处理器统计信息
   */
  public getStats(): any {
    // 这里应该返回处理器的统计信息
    // 实际实现需要跟踪处理次数、成功率等
    return {
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      averageProcessingTime: 0
    };
  }
}
